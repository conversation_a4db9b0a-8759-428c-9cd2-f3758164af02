"""
Helper functions for the SP500 price predictor.
"""

import os
import json
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Any, Optional, Dict
import exchange_calendars as ecals
import pandas as pd
# Third-party imports
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.figure import Figure

# Import configuration
from predictor.config import DATE_FORMAT, OUTPUT_DIR

# Import logging configuration
from utils.logging_config import get_predictor_logger

# Configure logger for this module
logger = get_predictor_logger(__name__)


def save_json(data: Any, filename: str, directory: Optional[Path] = None) -> str:
    """
    Save data to a JSON file.

    Args:
        data: Data to save.
        filename: Name of the file.
        directory: Directory to save the file in. Defaults to OUTPUT_DIR.

    Returns:
        Path to the saved file.

    Raises:
        ValueError: If filename is empty or None.
        TypeError: If data cannot be serialized to JSON.
        OSError: If there's an error creating the directory or writing the file.
    """
    if not filename:
        raise ValueError("Filename cannot be empty")

    if directory is None:
        directory = OUTPUT_DIR

    try:
        # Create directory if it doesn't exist
        os.makedirs(directory, exist_ok=True)

        # Add .json extension if not present
        if not filename.endswith('.json'):
            filename += '.json'

        filepath = os.path.join(directory, filename)

        # Validate that data is JSON serializable
        try:
            json.dumps(data)
        except (TypeError, OverflowError) as e:
            logger.error(f"Data is not JSON serializable: {str(e)}")
            raise TypeError(f"Data is not JSON serializable: {str(e)}")

        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=4)

        logger.info(f"Saved data to {filepath}")
        return filepath
    except OSError as e:
        logger.error(f"Error saving JSON to {filepath}: {str(e)}")
        raise


def load_json(filepath: str) -> Any:
    """
    Load data from a JSON file.

    Args:
        filepath: Path to the JSON file.

    Returns:
        Loaded data.

    Raises:
        FileNotFoundError: If the file doesn't exist.
        ValueError: If the file is not valid JSON.
        OSError: If there's an error reading the file.
    """
    if not filepath:
        raise ValueError("Filepath cannot be empty")

    if not os.path.exists(filepath):
        logger.error(f"File not found: {filepath}")
        raise FileNotFoundError(f"File not found: {filepath}")

    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            try:
                data = json.load(f)
            except json.JSONDecodeError as e:
                logger.error(f"Invalid JSON in {filepath}: {str(e)}")
                raise ValueError(f"Invalid JSON in {filepath}: {str(e)}")

        logger.info(f"Loaded data from {filepath}")
        return data
    except OSError as e:
        logger.error(f"Error reading file {filepath}: {str(e)}")
        raise


def plot_predictions(dates: List[str], actual: List[float], predicted: List[float],
                     title: str = "SP500 Price Predictions") -> Figure:
    """
    Plot actual vs. predicted values.

    Args:
        dates: List of date strings.
        actual: List of actual values.
        predicted: List of predicted values.
        title: Plot title.

    Returns:
        Matplotlib figure.
    """
    fig, ax = plt.subplots(figsize=(12, 6))

    # Convert to numpy arrays for calculations
    actual_np = np.array(actual)
    predicted_np = np.array(predicted)

    # Calculate error metrics
    mae = np.mean(np.abs(actual_np - predicted_np))
    rmse = np.sqrt(np.mean((actual_np - predicted_np) ** 2))

    # Plot data
    ax.plot(dates, actual, 'b-', label='Actual')
    ax.plot(dates, predicted, 'r--', label='Predicted')

    # Add error metrics to title
    full_title = f"{title}\nMAE: {mae:.4f}, RMSE: {rmse:.4f}"
    ax.set_title(full_title)
    ax.set_xlabel('Date')
    ax.set_ylabel('Return')
    ax.legend()

    # Rotate x-axis labels for better readability
    plt.xticks(rotation=45)

    # Add grid
    ax.grid(True, linestyle='--', alpha=0.7)

    # Tight layout
    fig.tight_layout()

    return fig


def save_plot(fig: Figure, filename: str, directory: Optional[Path] = None) -> str:
    """
    Save a matplotlib figure to a file.

    Args:
        fig: Matplotlib figure.
        filename: Name of the file.
        directory: Directory to save the file in. Defaults to OUTPUT_DIR.

    Returns:
        Path to the saved file.
    """
    if directory is None:
        directory = OUTPUT_DIR

    # Create directory if it doesn't exist
    os.makedirs(directory, exist_ok=True)

    # Add .png extension if not present
    if not filename.endswith('.png'):
        filename += '.png'

    filepath = os.path.join(directory, filename)

    fig.savefig(filepath, dpi=300, bbox_inches='tight')

    logger.info(f"Saved plot to {filepath}")
    return filepath


def get_next_trading_day(date_obj=None, code: str = "XNYS") -> Optional[datetime]:
    """
    Get the next trading day on or after the given date.

    Args:
        date_obj: A datetime object or date string. If None, uses today.
        code: Exchange code to use for trading calendar. Defaults to "XNYS" (NYSE).

    Returns:
        datetime: The next trading day.
    """
    # Handle None case
    if date_obj is None:
        date_obj = datetime.now()
        logger.debug(
            f"No date provided to get_next_trading_day, using current date: {date_obj}")

    # Handle string case
    if isinstance(date_obj, str):
        try:
            date_obj = datetime.strptime(date_obj, DATE_FORMAT)
            logger.debug(f"Converted string date to datetime: {date_obj}")
        except ValueError as e:
            logger.error(
                f"Invalid date format in get_next_trading_day: {date_obj}. Expected format: {DATE_FORMAT}. Error: {e}")
            # Return today as a fallback
            return None

    # Validate that date_obj is a datetime object
    if not isinstance(date_obj, datetime):
        logger.error(
            f"Invalid date object type in get_next_trading_day: {type(date_obj)}.")
        return None

    # Otherwise, go forward until we find a trading day
    try:
        # Get NYSE calendar
        nyse = ecals.get_calendar(code)
        next_trading_day = nyse.sessions_in_range(
            start=date_obj + pd.Timedelta(days=1), end=date_obj + pd.Timedelta(days=10))[0]
        return next_trading_day
    except Exception as e:
        logger.error(f"Error in get_next_trading_day: {e}")
        # Return today as a fallback
        return None


def format_news_for_prediction(news_texts: List[str], max_articles: int = 3) -> str:
    """
    Format news texts for model prediction.
    Optimized for BERT's shorter context length (512 tokens).

    Args:
        news_texts: List of news article texts.
        max_articles: Maximum number of articles to include.

    Returns:
        Formatted text for model input.
    """
    # Limit to max_articles (reduced for BERT's shorter context)
    if len(news_texts) > max_articles:
        news_texts = news_texts[:max_articles]

    # Truncate each article to approximately 150 words
    truncated_texts = []
    for text in news_texts:
        words = text.split()
        if len(words) > 150:
            truncated_texts.append(" ".join(words[:150]) + "...")
        else:
            truncated_texts.append(text)

    # Join with separator
    return "\n\n---\n\n".join(truncated_texts)


def calculate_directional_accuracy(actual, predicted):
    """
    Calculate directional accuracy - the percentage of times the prediction
    has the same sign as the label.

    Args:
        actual: Ground truth values (array-like)
        predicted: Predicted values (array-like)

    Returns:
        Directional accuracy as a float between 0 and 1
    """
    # Convert to numpy arrays if they aren't already
    actual_np = np.array(actual)
    predicted_np = np.array(predicted)

    # Get the sign of actual and predicted values (positive or negative)
    actual_signs = np.sign(actual_np)
    predicted_signs = np.sign(predicted_np)

    # Calculate accuracy (percentage of matching signs)
    return np.mean((predicted_signs == actual_signs).astype(float))
