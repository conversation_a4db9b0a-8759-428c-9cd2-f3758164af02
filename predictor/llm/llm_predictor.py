"""
LLM-based SP500 market predictor.

This module provides LLM-based prediction capabilities using news sentiment
and technical indicators to predict SP500 market direction.
"""

import asyncio
from datetime import datetime, timedelta
import hashlib
import json
from math import log
import traceback
from typing import Dict, List, Any, Optional, Union
import uuid
import pandas as pd

from apis.llm.base import BaseAPIManager
from apis.llm.openai import OpenAIManager
from apis.llm.anthropic import AnthropicManager
from apis.llm.gemini import GeminiManager
from apis.llm.data_types import CompletionRequest, CompletionStatus
from db.database import get_db_manager
from db.llm_api_service import LlmApiService
from predictor.config import MAX_ARTICLES_TO_ANALYZE
from predictor.llm.data_formatter import LLMDataFormatter
from predictor.llm.response_parser import LLMResponseParser
from utils.logging_config import get_predictor_logger

# Configure logger
logger = get_predictor_logger(__name__)

DEFAULT_MODEL_MAP = {
    'openai': 'o3',
    'anthropic': 'claude-opus-4-20250514',
    # 'gemini': 'gemini-2.5-pro',
    'gemini': 'gemini-2.5-flash',
}

class LLMPredictor:
    """LLM-based market predictor using news sentiment and technical indicators."""

    def __init__(
        self,
        api_configs: Optional[Dict[str, Dict[str, Any]]] = None,
        force_update: bool = False,
        cache_duration_hours: int = 3
    ):
        """
        Initialize the LLM predictor.

        Args:
            api_configs: Configuration for different LLM APIs
            force_update: Whether to force update the prediction
            cache_duration_hours: How long to cache predictions
        """
        self.force_update = force_update
        self.cache_duration_hours = cache_duration_hours

        # Initialize components
        self.data_formatter = LLMDataFormatter()
        self.response_parser = LLMResponseParser()
        self.db = get_db_manager()
        self.llm_service = LlmApiService(self.db.connection)

        # Initialize API managers
        self.api_managers = {}
        if api_configs:
            self._initialize_api_managers(api_configs)
        else:
            self._initialize_default_api_managers()

    def _initialize_api_managers(self, api_configs: Dict[str, Dict[str, Any]]):
        """Initialize API managers with custom configurations."""
        api_classes = {
            'openai': OpenAIManager,
            'anthropic': AnthropicManager,
            'gemini': GeminiManager
        }

        for api_name, config in api_configs.items():
            if api_name in api_classes:
                try:
                    self.api_managers[api_name] = api_classes[api_name](
                        **config)
                    logger.info(f"Initialized {api_name} API manager")
                except Exception as e:
                    logger.error(
                        f"Failed to initialize {api_name} API manager: {e}")

    def _initialize_default_api_managers(self):
        """Initialize API managers with default configurations."""
        try:
            self.api_managers['gemini'] = GeminiManager(
                total_budget=10.0,
                requests_per_minute=30
            )
            logger.info("Initialized Gemini API manager with defaults")
        except Exception as e:
            logger.error(f"Failed to initialize Gemini API manager: {e}")

        try:
            self.api_managers['openai'] = OpenAIManager(
                total_budget=10.0,
                requests_per_minute=30
            )
            logger.info("Initialized OpenAI API manager with defaults")
        except Exception as e:
            logger.error(f"Failed to initialize OpenAI API manager: {e}")

        try:
            self.api_managers['anthropic'] = AnthropicManager(
                total_budget=10.0,
                requests_per_minute=30
            )
            logger.info("Initialized Anthropic API manager with defaults")
        except Exception as e:
            logger.error(f"Failed to initialize Anthropic API manager: {e}")

    async def predict_market_direction(
        self,
        articles: List[Dict[str, Any]],
        current_price: float,
        price_data: Optional[pd.DataFrame] = None,
        vix_level: Optional[float] = None,
        preferred_api: str = "gemini",
        model_name: Optional[str] = None,
        max_articles: int = MAX_ARTICLES_TO_ANALYZE,
        system_prompt: Optional[str] = None,
        user_template: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Predict market direction using LLM analysis.

        Args:
            articles: List of news articles with sentiment scores
            current_price: Current SP500 price
            price_data: Historical price data for technical indicators
            vix_level: Current VIX level
            preferred_api: Preferred API to use for prediction
            model_name: Model name to use for prediction
            max_articles: Maximum number of articles to analyze
            system_prompt: Optional system prompt to use for prediction
            user_template: Optional user template to use for prediction

        Returns:
            Structured prediction result
        """
        try:
            # Check cache first
            if not self.force_update:
                logger.info("Checking for cached LLM prediction {}".format(preferred_api))
                cached_result = await self._get_cached_prediction(api=preferred_api)
                if cached_result:
                    logger.info("Returning cached LLM prediction")
                    logger.info(f"Cached result: {json.dumps(cached_result, indent=2, default=str)}")
                    return cached_result
            # Format technical indicators
            technical_indicators = self.data_formatter.format_technical_indicators(
                current_price=current_price,
                price_data=price_data,
                vix_level=vix_level
            )

            # Create prompt
            user_prompt, system_prompt = self.data_formatter.create_market_prediction_prompt(
                articles=articles,
                technical_indicators=technical_indicators,
                max_articles=max_articles,
                system_prompt=system_prompt,
                user_template=user_template
            )

            # Get prediction from LLM
            prediction_result = await self._get_llm_prediction(
                user_prompt=user_prompt,
                system_prompt=system_prompt,
                preferred_api=preferred_api,
                model_name=model_name
            )

            # Add article metadata to prediction result for reference mapping
            article_metadata = self.data_formatter.get_article_metadata()
            prediction_result = self._enrich_prediction_with_article_refs(
                prediction_result, article_metadata
            )

            # Add input metadata
            prediction_result['input_metadata'] = {
                'num_articles': len(articles),
                'current_price': current_price,
                'vix_level': vix_level,
                'technical_indicators': technical_indicators,
                'article_metadata': article_metadata
            }

            # Store the result
            await self._store_prediction(prediction_result)

            logger.info("LLM prediction completed successfully")
            logger.info(f"Prediction result: {json.dumps(prediction_result, indent=2, default=str)}")
            return prediction_result

        except Exception as e:
            logger.error(f"Error in LLM market prediction: {e}")
            raise

    async def _get_llm_prediction(
        self,
        user_prompt: str,
        system_prompt: str,
        preferred_api: str = "gemini",
        model_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """Get prediction from LLM API."""

        if preferred_api in self.api_managers:
            try:
                return await self._call_api(
                    self.api_managers[preferred_api],
                    user_prompt,
                    system_prompt,
                    preferred_api,
                    model_name
                )
            except Exception as e:
                logger.error(
                    f"Failed to get prediction from {preferred_api}: {e}")
                raise
        else:
            raise ValueError(f"API {preferred_api} is not supported or configured.")

    async def _call_api(
        self,
        api_manager: BaseAPIManager,
        user_prompt: str,
        system_prompt: str,
        api_name: str,
        model_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """Call specific LLM API for prediction."""

        # Get the appropriate model name for the API
        model_name = model_name or self._get_model_name_for_api(api_name)

        # Create completion request
        request = CompletionRequest(
            user_prompt=user_prompt,
            system_prompt=system_prompt,
            model=model_name,
            temperature=0.1  # Low temperature for consistent predictions
        )

        # Make API call
        response = api_manager.get_completion(request)

        if response.status != CompletionStatus.SUCCEEDED.value:
            raise Exception(f"API call failed with status: {response.status}")

        # Parse response
        prediction_result = self.response_parser.parse_market_prediction(
            response_content=response.content,
            api_name=api_name,
            model_name=model_name
        )
        prediction_metadata = prediction_result.get('metadata', {})
        prediction_metadata.update({
            'cost': response.cost,
            'input_tokens': response.input_tokens,
            'output_tokens': response.output_tokens
        })
        prediction_result['metadata'] = prediction_metadata

        return prediction_result

    def _enrich_prediction_with_article_refs(
        self,
        prediction_result: Dict[str, Any],
        article_metadata: Dict[str, Dict[str, str]]
    ) -> Dict[str, Any]:
        """Enrich prediction result with article URLs and metadata."""

        def enrich_evidence_list(evidence_list):
            """Enrich a list of evidence items with article metadata."""
            if not evidence_list or evidence_list is None:
                return []

            enriched_evidence = []
            for evidence in evidence_list:
                if evidence is None:
                    continue
                if isinstance(evidence, dict) and evidence.get('article_ref'):
                    article_ref = evidence['article_ref']
                    if isinstance(article_ref, int):
                        article_ref = f"Article {article_ref}"
                    if article_ref and article_ref in article_metadata:
                        metadata = article_metadata[article_ref]
                        evidence['url'] = metadata.get('url', '')
                        evidence['publish_time'] = metadata.get(
                            'publish_time', '')
                        evidence['article_title'] = metadata.get('title', '')
                        evidence['source'] = metadata.get('source', '')
                enriched_evidence.append(evidence)
            return enriched_evidence

        # Enrich key evidence
        if 'key_evidence' in prediction_result:
            prediction_result['key_evidence'] = enrich_evidence_list(
                prediction_result['key_evidence']
            )

        # Enrich detailed outlook sections
        if 'detailed_outlook' in prediction_result:
            for timeframe in ['short_term', 'medium_term', 'long_term']:
                if timeframe in prediction_result['detailed_outlook']:
                    outlook = prediction_result['detailed_outlook'][timeframe]
                    if 'key_evidence' in outlook:
                        outlook['key_evidence'] = enrich_evidence_list(
                            outlook['key_evidence']
                        )

        # Enrich dominant theme supporting articles
        if 'dominant_theme' in prediction_result and isinstance(prediction_result['dominant_theme'], dict):
            theme = prediction_result['dominant_theme']
            if 'supporting_articles' in theme and theme['supporting_articles'] is not None:
                enriched_articles = []
                for article_ref in theme['supporting_articles']:
                    if isinstance(article_ref, int):
                        article_ref = f"Article {article_ref}"
                    article = {'article_ref': article_ref}
                    if article_ref and article_ref in article_metadata:
                        metadata = article_metadata[article_ref]
                        article['url'] = metadata.get('url', '')
                        article['publish_time'] = metadata.get(
                            'publish_time', '')
                        article['article_title'] = metadata.get('title', '')
                        article['source'] = metadata.get('source', '')
                    enriched_articles.append(article)
                theme['supporting_articles'] = enriched_articles

        return prediction_result

    def _get_model_name_for_api(self, api_name: str) -> str:
        """Get the appropriate model name for the given API."""
        return DEFAULT_MODEL_MAP.get(api_name)

    async def _get_cached_prediction(self, api: str = "gemini") -> Optional[Dict[str, Any]]:
        """Get stored predictions if available and recent."""
        if not self.llm_service:
            return None

        try:            
            # Query database for stored predictions
            stored_result = self.llm_service.get_stored_prediction(api=api)
            
            if stored_result and len(stored_result) > 0:
                logger.info(f"Found {len(stored_result)} stored predictions for {api}")
                # Return the first prediction data if multiple are found
                return stored_result[0]['prediction_data']
            
            return None

        except Exception as e:
            logger.error(f"Error checking stored predictions: {e}")
            return None

    async def _store_prediction(self, prediction_result: Dict[str, Any]):
        """Store prediction result."""
        if not self.llm_service:
            return

        try:            
            # Extract metadata from prediction result
            metadata = prediction_result.get('metadata', {})
            input_metadata = prediction_result.get('input_metadata', {})
            
            # Prepare storage data
            storage_data = {
                'id': str(uuid.uuid4()),
                'api': metadata.get('api', 'unknown'),
                'model': metadata.get('model'),
                'prediction_data': prediction_result,
                'input_metadata': input_metadata,
                'created_at': datetime.now(),
                'expires_at': datetime.now() + timedelta(hours=self.cache_duration_hours),
                'cost': metadata.get('cost'),
                'input_tokens': metadata.get('input_tokens'),
                'output_tokens': metadata.get('output_tokens')
            }
            
            # Store in database
            prediction_id = self.llm_service.store_prediction(storage_data)
            logger.info(f"Stored prediction with ID: {prediction_id}")

        except Exception as e:
            logger.error(f"Error storing prediction: {e}")
            print(traceback.format_exc())

    def get_supported_apis(self) -> List[str]:
        """Get list of supported/initialized APIs."""
        return list(self.api_managers.keys())

    async def test_api_connection(self, api_name: str) -> bool:
        """Test connection to specific API."""
        if api_name not in self.api_managers:
            return False

        try:
            # Simple test request
            model_name = self._get_model_name_for_api(api_name)
            test_request = CompletionRequest(
                user_prompt="Test",
                system_prompt="Respond with 'OK'",
                model=model_name,
                max_tokens=10,
                temperature=0.0
            )

            response = self.api_managers[api_name]._make_completion_request(
                test_request)
            return response.status == CompletionStatus.SUCCEEDED.value

        except Exception as e:
            logger.error(f"API test failed for {api_name}: {e}")
            return False
