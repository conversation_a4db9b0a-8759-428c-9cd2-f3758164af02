displayName: "NewsMonitor High Error Rate"
documentation:
  content: "Alert when NewsMonitor application error rate exceeds 5% over 5 minutes"
  mimeType: "text/markdown"
conditions:
  - displayName: "High error rate condition"
    conditionThreshold:
      filter: 'resource.type="cloud_run_revision" AND resource.labels.service_name="newsmonitor-web"'
      aggregations:
        - alignmentPeriod: "300s"
          perSeriesAligner: "ALIGN_RATE"
          crossSeriesReducer: "REDUCE_SUM"
          groupByFields:
            - "resource.labels.service_name"
      comparison: "COMPARISON_GREATER_THAN"
      thresholdValue: 0.05
      duration: "300s"
      trigger:
        count: 1
combiner: "OR"
enabled: true
notificationChannels: []
alertStrategy:
  autoClose: "1800s"
