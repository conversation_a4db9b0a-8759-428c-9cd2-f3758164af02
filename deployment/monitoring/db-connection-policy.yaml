displayName: "NewsMonitor Database Connection Failures"
documentation:
  content: "Alert when database connection failures occur in NewsMonitor"
  mimeType: "text/markdown"
conditions:
  - displayName: "Database connection failures"
    conditionThreshold:
      filter: 'resource.type="cloud_run_revision" AND resource.labels.service_name="newsmonitor-web" AND textPayload:"Database connection failed"'
      aggregations:
        - alignmentPeriod: "300s"
          perSeriesAligner: "ALIGN_COUNT"
          crossSeriesReducer: "REDUCE_SUM"
      comparison: "COMPARISON_GREATER_THAN"
      thresholdValue: 3
      duration: "300s"
      trigger:
        count: 1
combiner: "OR"
enabled: true
notificationChannels: []
alertStrategy:
  autoClose: "1800s"
