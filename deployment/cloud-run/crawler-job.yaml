apiVersion: run.googleapis.com/v1
kind: Job
metadata:
  name: newsmonitor-crawler
spec:
  template:
    metadata:
      annotations:
        run.googleapis.com/execution-environment: gen2
        run.googleapis.com/cloudsql-instances: PROJECT_ID:REGION:newsmonitor-db
    spec:
      taskCount: 1
      template:
        spec:
          containers:
          - name: newsmonitor-crawler
            image: gcr.io/PROJECT_ID/newsmonitor-crawler:latest
            env:
            # Add flag to indicate cloud deployment
            - name: DEPLOYMENT_ENVIRONMENT
              value: "cloud"
            # Add version tag for tracking
            - name: DEPLOYMENT_VERSION
              value: "v2024-06-24-001"
            - name: DATABASE_URL
              valueFrom:
                secretKeyRef:
                  key: '1'
                  name: database-url
            - name: OPENAI_API_KEY
              valueFrom:
                secretKeyRef:
                  key: '1'
                  name: openai-api-keys
            - name: ANTHROPIC_API_KEY
              valueFrom:
                secretKeyRef:
                  key: '1'
                  name: anthropic-api-keys
            - name: GEMINI_API_KEY
              valueFrom:
                secretKeyRef:
                  key: '1'
                  name: gemini-api-keys
            resources:
              limits:
                cpu: 4000m
                memory: 4Gi
          maxRetries: 1
          timeoutSeconds: '3000'
          serviceAccountName: newsmonitor-crawler-sa