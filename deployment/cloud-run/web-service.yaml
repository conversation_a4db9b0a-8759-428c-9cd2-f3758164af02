apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: newsmonitor-web
  annotations:
    run.googleapis.com/ingress: all
spec:
  template:
    metadata:
      annotations:      
        # Scaling configuration
        autoscaling.knative.dev/minScale: "0"
        autoscaling.knative.dev/maxScale: "10"
        run.googleapis.com/cpu-throttling: "false"
        run.googleapis.com/execution-environment: gen2
        
        # Cloud SQL connection (public IP with Cloud SQL Auth Proxy)
        run.googleapis.com/cloudsql-instances: PROJECT_ID:REGION:newsmonitor-db
        
        # Startup probe configuration
        run.googleapis.com/startup-cpu-boost: "true"
    spec:
      containerConcurrency: 80
      timeoutSeconds: 300
      serviceAccountName: newsmonitor-web-sa
      containers:
      - name: newsmonitor-web
        image: gcr.io/PROJECT_ID/newsmonitor-web:latest
        ports:
        - name: http1
          containerPort: 8080
        env:
        - name: SERVICE_TYPE
          value: "web"
        - name: SERVICE_NAME
          value: "newsmonitor-web"
        - name: FLASK_ENV
          value: "production"
        - name: BASE_URL
          value: "https://cognitmarket.com"
        - name: SERVER_NAME
          value: "cognitmarket.com"
        - name: PREFERRED_URL_SCHEME
          value: "https"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: database-url
              key: '1'
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: flask-key
              key: '1'
        - name: SECURITY_PASSWORD_SALT
          valueFrom:
            secretKeyRef:
              name: flask-password-salt
              key: '1'
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: openai-api-keys
              key: '1'
        - name: ANTHROPIC_API_KEY
          valueFrom:
            secretKeyRef:
              name: anthropic-api-keys
              key: '1'
        - name: GEMINI_API_KEY
          valueFrom:
            secretKeyRef:
              name: gemini-api-keys
              key: '1'
        - name: GOOGLE_CLOUD_PROJECT
          value: "PROJECT_ID"
        - name: GOOGLE_CLOUD_LOCATION
          value: "REGION"
        - name: MAIL_SERVER
          value: "smtp.gmail.com"
        - name: MAIL_PORT
          value: "587"
        - name: MAIL_USERNAME
          valueFrom:
            secretKeyRef:
              name: email-username
              key: '1'
        - name: MAIL_PASSWORD
          valueFrom:
            secretKeyRef:
              name: email-password
              key: '1'
        - name: MAIL_DEFAULT_SENDER
          valueFrom:
            secretKeyRef:
              name: email-default-sender
              key: '1'
        # Add flag to disable Celery
        - name: DISABLE_CELERY
          value: "true"
        # Add flag to indicate cloud deployment
        - name: DEPLOYMENT_ENVIRONMENT
          value: "cloud"
        # Add version tag for tracking
        - name: DEPLOYMENT_VERSION
          value: "v2024-06-24-001"
        resources:
          limits:
            cpu: "1000m"
            memory: "2Gi"
          requests:
            cpu: "500m"
            memory: "1Gi"
        livenessProbe:
          httpGet:
            path: /health/live
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /health/startup
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 30
