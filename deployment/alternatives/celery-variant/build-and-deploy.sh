#!/bin/bash

# Build and deploy NewsMonitor application to Google Cloud Platform
# This script builds Docker images and deploys them to Cloud Run

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ID=${GOOGLE_CLOUD_PROJECT:-$(gcloud config get-value project)}
REGION=${GOOGLE_CLOUD_LOCATION:-"us-central1"}
REGISTRY="gcr.io"
IMAGE_TAG=${IMAGE_TAG:-"latest"}

# Get Redis IP
REDIS_IP=$(gcloud redis instances describe newsmonitor-redis \
    --region="${REGION}" \
    --project="${PROJECT_ID}" \
    --format="value(host)" 2>/dev/null || echo "REDIS_IP_NOT_FOUND")

echo -e "${GREEN}Building and deploying NewsMonitor to project: ${PROJECT_ID}${NC}"
echo -e "${YELLOW}Region: ${REGION}${NC}"
echo -e "${YELLOW}Registry: ${REGISTRY}${NC}"
echo -e "${YELLOW}Image Tag: ${IMAGE_TAG}${NC}"
echo -e "${YELLOW}Redis IP: ${REDIS_IP}${NC}"

# Function to build and push Docker image
build_and_push() {
    local service_name=$1
    local dockerfile=$2
    local context_dir=${3:-"."}
    
    echo -e "${BLUE}=== Building ${service_name} ===${NC}"
    
    local image_name="${REGISTRY}/${PROJECT_ID}/newsmonitor-${service_name}:${IMAGE_TAG}"
    
    # Build the image
    echo -e "${YELLOW}Building Docker image: ${image_name}${NC}"
    docker build -f "${dockerfile}" -t "${image_name}" "${context_dir}"
    
    # Push to registry
    echo -e "${YELLOW}Pushing to registry...${NC}"
    docker push "${image_name}"
    
    echo -e "${GREEN}Successfully built and pushed: ${image_name}${NC}"
}

# Function to deploy to Cloud Run
deploy_service() {
    local service_name=$1
    local service_config=$2
    
    echo -e "${BLUE}=== Deploying ${service_name} to Cloud Run ===${NC}"
    
    # Replace placeholders in service config
    local temp_config="/tmp/${service_name}-service.yaml"
    sed -e "s/PROJECT_ID/${PROJECT_ID}/g" \
        -e "s/REGION/${REGION}/g" \
        -e "s/REDIS_IP/${REDIS_IP}/g" \
        "${service_config}" > "${temp_config}"
    
    # Deploy the service
    echo -e "${YELLOW}Deploying service: ${service_name}${NC}"
    gcloud run services replace "${temp_config}" \
        --region="${REGION}" \
        --project="${PROJECT_ID}"
    
    # Clean up temp file
    rm "${temp_config}"
    
    echo -e "${GREEN}Successfully deployed: ${service_name}${NC}"
}

# Change to project root directory
cd "$(dirname "$0")/../.."

echo -e "${GREEN}=== Configuring Docker for GCR ===${NC}"
gcloud auth configure-docker

echo -e "${GREEN}=== Building Base Image ===${NC}"
build_and_push "base" "deployment/docker/Dockerfile.base"

echo -e "${GREEN}=== Building Application Images ===${NC}"

# Build web application
build_and_push "web" "deployment/docker/Dockerfile.web"

# Build crawler
build_and_push "crawler" "deployment/docker/Dockerfile.crawler"

# Build Celery worker
build_and_push "celery" "deployment/docker/Dockerfile.celery"

echo -e "${GREEN}=== Deploying Services to Cloud Run ===${NC}"

# Deploy web application
deploy_service "web" "deployment/cloud-run/web-service.yaml"

# Deploy Celery worker
deploy_service "celery" "deployment/cloud-run/celery-service.yaml"

echo -e "${GREEN}=== Setting up Traffic Allocation ===${NC}"

# Allocate 100% traffic to latest revision
gcloud run services update-traffic newsmonitor-web \
    --to-latest \
    --region="${REGION}" \
    --project="${PROJECT_ID}"

echo -e "${GREEN}=== Getting Service URLs ===${NC}"

WEB_URL=$(gcloud run services describe newsmonitor-web \
    --region="${REGION}" \
    --project="${PROJECT_ID}" \
    --format="value(status.url)")

echo -e "${GREEN}=== Deployment Complete ===${NC}"
echo ""
echo -e "${GREEN}Service URLs:${NC}"
echo "  Web Application: ${WEB_URL}"
echo ""
echo -e "${YELLOW}Next steps:${NC}"
echo "1. Set up Cloud Scheduler jobs: ./setup-scheduler.sh"
echo "2. Configure custom domain (optional): ./setup-domain.sh"
echo "3. Set up monitoring: ./setup-monitoring.sh"
echo "4. Test the application: curl ${WEB_URL}/health"
echo ""
echo -e "${BLUE}Useful commands:${NC}"
echo "  View logs: gcloud run services logs read newsmonitor-web --region=${REGION}"
echo "  Update service: gcloud run services update newsmonitor-web --region=${REGION}"
echo "  Scale service: gcloud run services update newsmonitor-web --max-instances=20 --region=${REGION}"
