#!/bin/bash

# Setup Cloud Scheduler jobs for NewsMonitor application
# This script creates scheduled jobs for crawler execution and maintenance tasks

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ID=${GOOGLE_CLOUD_PROJECT:-$(gcloud config get-value project)}
REGION=${GOOGLE_CLOUD_LOCATION:-"us-central1"}

echo -e "${GREEN}Setting up Cloud Scheduler jobs for NewsMonitor${NC}"

# Function to create or update scheduler job
create_scheduler_job() {
    local job_name=$1
    local schedule=$2
    local description=$3
    local service_url=$4
    local http_method=${5:-"POST"}
    local payload=${6:-"{}"}
    local timeout=${7:-"3600s"}
    
    echo -e "${YELLOW}Creating scheduler job: ${job_name}${NC}"
    
    # Check if job exists
    if gcloud scheduler jobs describe "${job_name}" --location="${REGION}" --project="${PROJECT_ID}" >/dev/null 2>&1; then
        echo -e "${YELLOW}Job ${job_name} exists. Updating...${NC}"
        gcloud scheduler jobs update http "${job_name}" \
            --location="${REGION}" \
            --schedule="${schedule}" \
            --uri="${service_url}" \
            --http-method="${http_method}" \
            --headers="Content-Type=application/json" \
            --body="${payload}" \
            --attempt-deadline="${timeout}" \
            --project="${PROJECT_ID}"
    else
        echo -e "${GREEN}Creating new job: ${job_name}${NC}"
        gcloud scheduler jobs create http "${job_name}" \
            --location="${REGION}" \
            --schedule="${schedule}" \
            --description="${description}" \
            --uri="${service_url}" \
            --http-method="${http_method}" \
            --headers="Content-Type=application/json" \
            --body="${payload}" \
            --attempt-deadline="${timeout}" \
            --max-retry-attempts=3 \
            --max-retry-duration=1800s \
            --min-backoff-duration=5s \
            --max-backoff-duration=300s \
            --project="${PROJECT_ID}"
    fi
}

# Get service URLs
echo -e "${YELLOW}Getting service URLs...${NC}"

WEB_URL=$(gcloud run services describe newsmonitor-web \
    --region="${REGION}" \
    --project="${PROJECT_ID}" \
    --format="value(status.url)" 2>/dev/null || echo "")

if [ -z "${WEB_URL}" ]; then
    echo -e "${RED}Error: Could not get web service URL. Make sure the service is deployed.${NC}"
    exit 1
fi

echo -e "${GREEN}Web Service URL: ${WEB_URL}${NC}"

echo -e "${GREEN}=== Creating Crawler Jobs ===${NC}"

# News crawler job - every 3 hours
create_scheduler_job \
    "newsmonitor-crawler-3h" \
    "0 */3 * * *" \
    "Run NewsMonitor crawler every 3 hours" \
    "${WEB_URL}/api/admin/run-crawler" \
    "POST" \
    '{"spiders": "all", "log_level": "INFO"}' \
    "3600s"

# Daily full crawler job - once per day at 2 AM
create_scheduler_job \
    "newsmonitor-crawler-daily" \
    "0 2 * * *" \
    "Run NewsMonitor full crawler daily" \
    "${WEB_URL}/api/admin/run-crawler" \
    "POST" \
    '{"spiders": "all", "log_level": "INFO", "full_crawl": true}' \
    "7200s"

echo -e "${GREEN}=== Creating Maintenance Jobs ===${NC}"

# Database cleanup job - daily at 3 AM
create_scheduler_job \
    "newsmonitor-db-cleanup" \
    "0 3 * * *" \
    "Clean up old database records" \
    "${WEB_URL}/api/admin/cleanup-database" \
    "POST" \
    '{"days_to_keep": 90}' \
    "1800s"

# Model retraining job - weekly on Sunday at 4 AM
create_scheduler_job \
    "newsmonitor-retrain-models" \
    "0 4 * * 0" \
    "Retrain prediction models weekly" \
    "${WEB_URL}/api/admin/retrain-models" \
    "POST" \
    '{"model_types": ["logistic_regression", "gradient_boosting"]}' \
    "7200s"

# Health check job - every 5 minutes
create_scheduler_job \
    "newsmonitor-health-check" \
    "*/5 * * * *" \
    "Health check for NewsMonitor services" \
    "${WEB_URL}/health" \
    "GET" \
    "" \
    "30s"

echo -e "${GREEN}=== Creating Email Jobs ===${NC}"

# Daily summary email - every day at 10 AM ET
create_scheduler_job \
    "newsmonitor-daily-summary" \
    "0 10 * * *" \
    "Send daily summary emails" \
    "${WEB_URL}/api/admin/send-daily-summary" \
    "POST" \
    '{"email_type": "daily_summary"}' \
    "600s"

# Weekly market analysis - every Monday at 9 AM ET
create_scheduler_job \
    "newsmonitor-weekly-analysis" \
    "0 9 * * 1" \
    "Send weekly market analysis emails" \
    "${WEB_URL}/api/admin/send-weekly-analysis" \
    "POST" \
    '{"email_type": "weekly_analysis"}' \
    "900s"

echo -e "${GREEN}=== Scheduler Setup Complete ===${NC}"
echo ""
echo -e "${GREEN}Created scheduler jobs:${NC}"
echo "  - newsmonitor-crawler-3h (every 3 hours)"
echo "  - newsmonitor-crawler-daily (daily at 2 AM)"
echo "  - newsmonitor-db-cleanup (daily at 3 AM)"
echo "  - newsmonitor-retrain-models (weekly on Sunday at 4 AM)"
echo "  - newsmonitor-health-check (every 5 minutes)"
echo "  - newsmonitor-daily-summary (daily at 10 AM ET)"
echo "  - newsmonitor-weekly-analysis (weekly on Monday at 9 AM ET)"
echo ""
echo -e "${YELLOW}Management commands:${NC}"
echo "  List jobs: gcloud scheduler jobs list --location=${REGION}"
echo "  Run job now: gcloud scheduler jobs run JOB_NAME --location=${REGION}"
echo "  Pause job: gcloud scheduler jobs pause JOB_NAME --location=${REGION}"
echo "  Resume job: gcloud scheduler jobs resume JOB_NAME --location=${REGION}"
echo "  View logs: gcloud logging read 'resource.type=\"cloud_scheduler_job\"' --limit=50"
