#!/bin/bash

# Setup GCP infrastructure for NewsMonitor application
# This script creates Cloud SQL, Redis, VPC, and other required resources

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ID=${GOOGLE_CLOUD_PROJECT:-$(gcloud config get-value project)}
REGION=${GOOGLE_CLOUD_LOCATION:-"us-central1"}
ZONE="${REGION}-a"

echo -e "${GREEN}Setting up infrastructure for NewsMonitor in project: ${PROJECT_ID}${NC}"
echo -e "${YELLOW}Region: ${REGION}${NC}"
echo -e "${YELLOW}Zone: ${ZONE}${NC}"

# Function to check if resource exists
resource_exists() {
    local resource_type=$1
    local resource_name=$2
    local additional_flags=$3
    
    gcloud ${resource_type} describe ${resource_name} ${additional_flags} >/dev/null 2>&1
}

echo -e "${GREEN}=== Setting up VPC Network ===${NC}"

# Create VPC network
if ! resource_exists "compute networks" "newsmonitor-vpc" "--project=${PROJECT_ID}"; then
    echo -e "${YELLOW}Creating VPC network...${NC}"
    gcloud compute networks create newsmonitor-vpc \
        --subnet-mode=custom \
        --project="${PROJECT_ID}"
else
    echo -e "${GREEN}VPC network already exists${NC}"
fi

# Create subnet
if ! resource_exists "compute networks subnets" "newsmonitor-subnet" "--region=${REGION} --project=${PROJECT_ID}"; then
    echo -e "${YELLOW}Creating subnet...${NC}"
    gcloud compute networks subnets create newsmonitor-subnet \
        --network=newsmonitor-vpc \
        --range=10.0.0.0/24 \
        --region="${REGION}" \
        --project="${PROJECT_ID}"
else
    echo -e "${GREEN}Subnet already exists${NC}"
fi

# Create VPC connector for Cloud Run
if ! resource_exists "compute networks vpc-access connectors" "newsmonitor-connector" "--region=${REGION} --project=${PROJECT_ID}"; then
    echo -e "${YELLOW}Creating VPC connector...${NC}"
    gcloud compute networks vpc-access connectors create newsmonitor-connector \
        --network=newsmonitor-vpc \
        --region="${REGION}" \
        --subnet=newsmonitor-subnet \
        --subnet-project="${PROJECT_ID}" \
        --min-instances=2 \
        --max-instances=10 \
        --machine-type=e2-micro \
        --project="${PROJECT_ID}"
else
    echo -e "${GREEN}VPC connector already exists${NC}"
fi

echo -e "${GREEN}=== Setting up Cloud SQL ===${NC}"

# Create Cloud SQL instance
if ! resource_exists "sql instances" "newsmonitor-db" "--project=${PROJECT_ID}"; then
    echo -e "${YELLOW}Creating Cloud SQL instance...${NC}"
    gcloud sql instances create newsmonitor-db \
        --database-version=POSTGRES_14 \
        --tier=db-n1-standard-1 \
        --region="${REGION}" \
        --network=newsmonitor-vpc \
        --no-assign-ip \
        --storage-type=SSD \
        --storage-size=20GB \
        --storage-auto-increase \
        --backup-start-time=03:00 \
        --maintenance-window-day=SUN \
        --maintenance-window-hour=04 \
        --deletion-protection \
        --project="${PROJECT_ID}"
    
    echo -e "${YELLOW}Waiting for Cloud SQL instance to be ready...${NC}"
    gcloud sql instances patch newsmonitor-db \
        --authorized-networks=0.0.0.0/0 \
        --project="${PROJECT_ID}"
else
    echo -e "${GREEN}Cloud SQL instance already exists${NC}"
fi

# Create database
echo -e "${YELLOW}Creating database...${NC}"
gcloud sql databases create articles_db \
    --instance=newsmonitor-db \
    --project="${PROJECT_ID}" || true

# Create database user
echo -e "${YELLOW}Creating database user...${NC}"
DB_PASSWORD=$(openssl rand -base64 32)
gcloud sql users create newsmonitor-user \
    --instance=newsmonitor-db \
    --password="${DB_PASSWORD}" \
    --project="${PROJECT_ID}" || true

echo -e "${GREEN}Database user created with password: ${DB_PASSWORD}${NC}"
echo -e "${YELLOW}Please save this password and update your secrets!${NC}"

echo -e "${GREEN}=== Setting up Cloud Memorystore (Redis) ===${NC}"

# Create Redis instance
if ! resource_exists "redis instances" "newsmonitor-redis" "--region=${REGION} --project=${PROJECT_ID}"; then
    echo -e "${YELLOW}Creating Redis instance...${NC}"
    gcloud redis instances create newsmonitor-redis \
        --size=1 \
        --region="${REGION}" \
        --network=newsmonitor-vpc \
        --redis-version=redis_6_x \
        --project="${PROJECT_ID}"
    
    echo -e "${YELLOW}Waiting for Redis instance to be ready...${NC}"
    sleep 60
else
    echo -e "${GREEN}Redis instance already exists${NC}"
fi

# Get Redis IP
REDIS_IP=$(gcloud redis instances describe newsmonitor-redis \
    --region="${REGION}" \
    --project="${PROJECT_ID}" \
    --format="value(host)")

echo -e "${GREEN}Redis IP: ${REDIS_IP}${NC}"

echo -e "${GREEN}=== Setting up Cloud Storage ===${NC}"

# Create storage bucket for logs and artifacts
BUCKET_NAME="${PROJECT_ID}-newsmonitor-storage"
if ! gsutil ls -b "gs://${BUCKET_NAME}" >/dev/null 2>&1; then
    echo -e "${YELLOW}Creating storage bucket...${NC}"
    gsutil mb -p "${PROJECT_ID}" -c STANDARD -l "${REGION}" "gs://${BUCKET_NAME}"
    
    # Set bucket lifecycle
    cat > /tmp/lifecycle.json << EOF
{
  "lifecycle": {
    "rule": [
      {
        "action": {"type": "Delete"},
        "condition": {"age": 90}
      }
    ]
  }
}
EOF
    gsutil lifecycle set /tmp/lifecycle.json "gs://${BUCKET_NAME}"
    rm /tmp/lifecycle.json
else
    echo -e "${GREEN}Storage bucket already exists${NC}"
fi

echo -e "${GREEN}=== Setting up Cloud Scheduler ===${NC}"

# Create App Engine app (required for Cloud Scheduler)
if ! gcloud app describe --project="${PROJECT_ID}" >/dev/null 2>&1; then
    echo -e "${YELLOW}Creating App Engine app for Cloud Scheduler...${NC}"
    gcloud app create --region="${REGION}" --project="${PROJECT_ID}"
else
    echo -e "${GREEN}App Engine app already exists${NC}"
fi

echo -e "${GREEN}=== Infrastructure Setup Complete ===${NC}"
echo ""
echo -e "${GREEN}Created resources:${NC}"
echo "  - VPC Network: newsmonitor-vpc"
echo "  - Subnet: newsmonitor-subnet (10.0.0.0/24)"
echo "  - VPC Connector: newsmonitor-connector"
echo "  - Cloud SQL: newsmonitor-db (PostgreSQL 14)"
echo "  - Database: articles_db"
echo "  - Database User: newsmonitor-user"
echo "  - Redis: newsmonitor-redis (${REDIS_IP})"
echo "  - Storage Bucket: gs://${BUCKET_NAME}"
echo "  - App Engine: ${REGION}"
echo ""
echo -e "${YELLOW}Important information:${NC}"
echo "  - Database Password: ${DB_PASSWORD}"
echo "  - Redis IP: ${REDIS_IP}"
echo "  - Cloud SQL Connection: ${PROJECT_ID}:${REGION}:newsmonitor-db"
echo ""
echo -e "${YELLOW}Next steps:${NC}"
echo "1. Update your secrets with the database password and Redis IP"
echo "2. Run ./build-and-deploy.sh to build and deploy the application"
echo "3. Set up Cloud Scheduler jobs with ./setup-scheduler.sh"
