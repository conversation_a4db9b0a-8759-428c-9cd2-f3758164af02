apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: newsmonitor-celery
  annotations:
    run.googleapis.com/ingress: none
    run.googleapis.com/execution-environment: gen2
spec:
  template:
    metadata:
      annotations:
        # Scaling configuration - keep at least 1 instance running
        autoscaling.knative.dev/minScale: "1"
        autoscaling.knative.dev/maxScale: "3"
        run.googleapis.com/cpu-throttling: "false"
        
        # VPC connector for private resources
        run.googleapis.com/vpc-access-connector: newsmonitor-connector
        run.googleapis.com/vpc-access-egress: private-ranges-only
        
        # Cloud SQL connection
        run.googleapis.com/cloudsql-instances: PROJECT_ID:REGION:newsmonitor-db
    spec:
      containerConcurrency: 1
      timeoutSeconds: 3600  # 1 hour for long-running tasks
      serviceAccountName: newsmonitor-celery-sa
      containers:
      - name: newsmonitor-celery
        image: gcr.io/PROJECT_ID/newsmonitor-celery:latest
        env:
        - name: SERVICE_TYPE
          value: "celery"
        - name: SERVICE_NAME
          value: "newsmonitor-celery"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: database-url
              key: url
        - name: CELERY_BROKER_URL
          value: "redis://REDIS_IP:6379/0"
        - name: CELERY_RESULT_BACKEND
          value: "redis://REDIS_IP:6379/0"
        - name: CELERY_WORKER_CONCURRENCY
          value: "4"
        - name: CELERY_WORKER_PREFETCH_MULTIPLIER
          value: "1"
        - name: CELERY_TASK_SOFT_TIME_LIMIT
          value: "300"
        - name: CELERY_TASK_TIME_LIMIT
          value: "600"
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: flask-secrets
              key: secret-key
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: llm-api-keys
              key: openai-key
        - name: ANTHROPIC_API_KEY
          valueFrom:
            secretKeyRef:
              name: llm-api-keys
              key: anthropic-key
        - name: GEMINI_API_KEY
          valueFrom:
            secretKeyRef:
              name: llm-api-keys
              key: gemini-key
        - name: GOOGLE_CLOUD_PROJECT
          value: "PROJECT_ID"
        - name: GOOGLE_CLOUD_LOCATION
          value: "REGION"
        - name: MAIL_SERVER
          value: "smtp.gmail.com"
        - name: MAIL_PORT
          value: "587"
        - name: MAIL_USERNAME
          valueFrom:
            secretKeyRef:
              name: email-config
              key: username
        - name: MAIL_PASSWORD
          valueFrom:
            secretKeyRef:
              name: email-config
              key: password
        - name: MAIL_DEFAULT_SENDER
          valueFrom:
            secretKeyRef:
              name: email-config
              key: default-sender
        resources:
          limits:
            cpu: "1000m"
            memory: "2Gi"
          requests:
            cpu: "500m"
            memory: "1Gi"
        livenessProbe:
          exec:
            command:
            - python
            - /app/deployment/scripts/health_check.py
          initialDelaySeconds: 60
          periodSeconds: 60
          timeoutSeconds: 30
          failureThreshold: 3
        readinessProbe:
          exec:
            command:
            - python
            - /app/deployment/scripts/health_check.py
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 15
          failureThreshold: 3
        volumeMounts:
        - name: service-account
          mountPath: /app/credentials
          readOnly: true
      volumes:
      - name: service-account
        secret:
          secretName: service-account-key
