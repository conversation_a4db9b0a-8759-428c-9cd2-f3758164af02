# Alternative Deployment Options

This directory contains alternative deployment configurations for NewsMonitor.

## Default Deployment (Recommended)

The main deployment uses **Cloud Scheduler** for background tasks, providing:
- **40-35% cost savings** ($43-83/month reduction)
- **Simplified architecture** (no Redis/Celery)
- **Better reliability** (fewer failure points)

See the main [deployment guide](../README.md) for the recommended approach.

## Celery-Based Alternative

### When to Use Celery Variant

Use the Celery-based deployment in `celery-variant/` if you need:

- **Real-time event processing**: Tasks triggered by user actions or external events
- **Complex task dependencies**: Workflows with task chains and dependencies
- **Advanced queue management**: Priority queues, task routing, custom retry logic
- **High-frequency task execution**: More than 1000 tasks per hour
- **Task result tracking**: Need to track and query task execution results

### Cost Comparison

| Component | Cloud Scheduler | Celery Variant | Difference |
|-----------|----------------|----------------|------------|
| **Cloud Run (Web)** | $20-100 | $20-100 | $0 |
| **Cloud Run (Celery)** | $0 | $15-45 | +$15-45 |
| **Cloud SQL** | $50-70 | $50-70 | $0 |
| **Redis/Memorystore** | $0 | $25-30 | +$25-30 |
| **VPC Connector** | $0 | $3-8 | +$3-8 |
| **Total** | **$77-192** | **$120-275** | **+$43-83** |

### Files in Celery Variant

```
celery-variant/
├── Dockerfile.celery           # Celery worker container
├── celery-service.yaml         # Cloud Run Celery service
├── web-service.yaml            # Web service with VPC/Redis
├── setup-infrastructure.sh    # Infrastructure with Redis/VPC
├── build-and-deploy.sh         # Deploy with Celery worker
└── setup-scheduler.sh          # Crawler scheduling only
```

### Migration Between Approaches

#### From Celery to Cloud Scheduler (Cost Optimization)
1. Deploy Cloud Scheduler endpoints alongside Celery
2. Test scheduler jobs in parallel
3. Switch traffic to scheduler endpoints
4. Remove Redis and Celery infrastructure

#### From Cloud Scheduler to Celery (Feature Requirements)
1. Deploy Redis and Celery infrastructure
2. Deploy Celery worker service
3. Update web service to use VPC/Redis
4. Migrate scheduler jobs to Celery tasks

## Documentation

- `COST_ANALYSIS.md` - Detailed cost comparison between approaches
- `CELERY_TO_SCHEDULER_MIGRATION.md` - Migration guide and considerations

## Recommendation

For most NewsMonitor deployments, the **Cloud Scheduler approach** is recommended because:

✅ **Significant cost savings** (40-35% reduction)
✅ **Simpler architecture** (fewer moving parts)
✅ **Better reliability** (no Redis dependency)
✅ **Easier maintenance** (fewer services to monitor)
✅ **Same functionality** for scheduled background tasks

The Celery variant should only be used if you have specific requirements that cannot be met with the Cloud Scheduler approach.
