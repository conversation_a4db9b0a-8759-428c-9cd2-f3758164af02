# NewsMonitor GCP Deployment Guide

Deploy the NewsMonitor financial news web crawler application to Google Cloud Platform with a cost-optimized, serverless architecture.

## 💰 Cost-Optimized Architecture

**Monthly Savings: $43-83 (40-35% reduction)**
- ✅ **Cloud Scheduler** replaces Celery/Redis (~$25-30/month saved)
- ✅ **Public IP** for Cloud SQL (~$3-8/month saved on VPC)
- ✅ **Serverless** auto-scaling containers
- ✅ **Simplified** infrastructure and maintenance

## Quick Start

### Prerequisites
```bash
# Set up environment
export GOOGLE_CLOUD_PROJECT="your-project-id"
export GOOGLE_CLOUD_LOCATION="us-central1"

# Enable required APIs
gcloud services enable run.googleapis.com sql-component.googleapis.com \
  sqladmin.googleapis.com secretmanager.googleapis.com \
  cloudbuild.googleapis.com cloudscheduler.googleapis.com
```

### 5-Step Deployment
```bash
cd deployment/scripts

# 1. Create infrastructure
./setup-infrastructure.sh

# 2. Configure secrets
./setup-secrets.sh

# 3. Build and deploy
./build-and-deploy.sh

# 4. Set up scheduler jobs
./setup-scheduler.sh

# 5. Test deployment
curl $WEB_URL/health
```

## Architecture Overview

### Services Used
```
┌─────────────────────────────────────────────────────────────────┐
│  Cloud Run (Web App + Scheduler Endpoints)                     │
│  ├── Auto-scaling: 1-10 instances                             │
│  ├── CPU: 1 vCPU, Memory: 2GB                                │
│  └── Background tasks via HTTP endpoints                       │
├─────────────────────────────────────────────────────────────────┤
│  Cloud SQL (PostgreSQL, Public IP)                             │
│  ├── Instance: db-n1-standard-1                               │
│  ├── Storage: 20GB SSD with auto-resize                       │
│  └── Secure connection via Cloud SQL Auth Proxy              │
├─────────────────────────────────────────────────────────────────┤
│  Cloud Scheduler (HTTP-based Background Tasks)                 │
│  ├── Daily email summaries                                     │
│  ├── Weekly email summaries                                    │
│  ├── Market alerts (hourly during market hours)               │
│  ├── Database cleanup                                          │
│  ├── Crawler execution (every 3 hours)                        │
│  └── Model retraining                                          │
├─────────────────────────────────────────────────────────────────┤
│  Secret Manager + Cloud Storage + Monitoring                   │
└─────────────────────────────────────────────────────────────────┘
```

### Cost Breakdown

| Service | Monthly Cost | Notes |
|---------|--------------|-------|
| **Cloud Run (Web)** | $20-100 | Includes scheduler endpoints |
| **Cloud Run (Crawler)** | $5-15 | On-demand execution |
| **Cloud SQL** | $50-70 | PostgreSQL with public IP |
| **Cloud Storage** | $1-5 | Logs and artifacts |
| **Cloud Scheduler** | $1-2 | All background tasks |
| **Total** | **$77-192** | **40-35% savings** |

## Background Tasks Migration

### HTTP Endpoints Replace Celery Tasks

| Original Celery Task | New HTTP Endpoint | Schedule |
|---------------------|-------------------|----------|
| `send_daily_summaries_task` | `POST /api/scheduler/daily-summary` | Daily 10 AM ET |
| `send_weekly_summaries_task` | `POST /api/scheduler/weekly-summary` | Monday 9 AM ET |
| `send_market_alert_task` | `POST /api/scheduler/market-alerts` | Hourly (market hours) |
| `cleanup_old_email_logs_task` | `POST /api/scheduler/cleanup-email-logs` | Weekly Sunday 4 AM |
| `send_targeted_emails_task` | `POST /api/scheduler/targeted-emails` | On-demand |

### Authentication & Security
```python
# Scheduler endpoints require Cloud Scheduler headers
@require_scheduler_auth
def send_daily_summaries():
    # Checks for X-CloudScheduler headers
    # Or admin authentication
    # Or Google-Cloud-Scheduler User-Agent
```

## Code Changes Required

### 1. Add Scheduler Endpoints to Web App
```python
# In web/app.py
from deployment.code_changes.scheduler_endpoints import scheduler_bp
app.register_blueprint(scheduler_bp)
```

### 2. Update Configuration (Remove Celery)
```python
# In web/config.py
class Config:
    # Remove Celery configuration
    # CELERY_BROKER_URL = ...
    # CELERY_RESULT_BACKEND = ...
    
    # Add scheduler flag
    DISABLE_CELERY = os.getenv('DISABLE_CELERY', 'false').lower() == 'true'
```

### 3. Conditional Celery Initialization
```python
# In web/__init__.py
def create_app():
    # ... existing code ...
    
    # Only initialize Celery if not disabled
    if not app.config.get('DISABLE_CELERY', False):
        init_celery(app)
    
    # Register scheduler endpoints
    from deployment.code_changes.scheduler_endpoints import scheduler_bp
    app.register_blueprint(scheduler_bp)
```

## Infrastructure Details

### Cloud SQL Configuration
- **Public IP**: Cost-optimized approach using Cloud SQL Auth Proxy
- **No VPC**: Eliminates VPC connector costs (~$3-8/month)
- **Secure**: Encrypted connections via Cloud SQL Auth Proxy
- **Authorized Networks**: 0.0.0.0/0 (secured by Cloud SQL Auth Proxy)

### Cloud Scheduler Jobs
```bash
# Daily email summaries
gcloud scheduler jobs create http newsmonitor-daily-summary \
  --schedule="0 10 * * *" \
  --uri="$WEB_URL/api/scheduler/daily-summary" \
  --http-method=POST \
  --headers="X-CloudScheduler=true"

# Weekly email summaries  
gcloud scheduler jobs create http newsmonitor-weekly-summary \
  --schedule="0 9 * * 1" \
  --uri="$WEB_URL/api/scheduler/weekly-summary" \
  --http-method=POST \
  --headers="X-CloudScheduler=true"

# Market alerts (hourly during market hours)
gcloud scheduler jobs create http newsmonitor-market-alerts \
  --schedule="0 9-16 * * 1-5" \
  --uri="$WEB_URL/api/scheduler/market-alerts" \
  --http-method=POST \
  --headers="X-CloudScheduler=true"
```

## Testing the Deployment

### Test Individual Endpoints
```bash
WEB_URL="https://your-service-url"

# Test daily summary
curl -X POST \
  -H "Content-Type: application/json" \
  -H "X-CloudScheduler: true" \
  "$WEB_URL/api/scheduler/daily-summary"

# Test market alerts
curl -X POST \
  -H "Content-Type: application/json" \
  -H "X-CloudScheduler: true" \
  "$WEB_URL/api/scheduler/market-alerts"
```

### Test Cloud Scheduler Jobs
```bash
# Run jobs manually
gcloud scheduler jobs run newsmonitor-daily-summary --location=$REGION
gcloud scheduler jobs run newsmonitor-market-alerts --location=$REGION

# Check job status
gcloud scheduler jobs list --location=$REGION

# View job logs
gcloud logging read 'resource.type="cloud_scheduler_job"' --limit=10
```

## Monitoring & Maintenance

### Cloud Scheduler Monitoring
```bash
# Job execution status
gcloud scheduler jobs describe JOB_NAME --location=$REGION

# Job execution history
gcloud logging read 'resource.type="cloud_scheduler_job" AND resource.labels.job_id="JOB_NAME"'
```

### Application Monitoring
```bash
# View web service logs
gcloud run services logs read newsmonitor-web --region=$REGION

# Monitor scheduler endpoint calls
gcloud logging read 'resource.type="cloud_run_revision" AND httpRequest.requestUrl:"/api/scheduler/"'
```

## Benefits Summary

### ✅ Cost Benefits
- **$43-83/month savings** (40-35% reduction)
- **$516-996/year savings**
- Simplified billing (fewer services)

### ✅ Operational Benefits
- **Simpler architecture** (fewer moving parts)
- **Better observability** (HTTP logs vs queue monitoring)
- **Easier debugging** (direct endpoint testing)
- **Built-in retry logic** (Cloud Scheduler handles retries)

### ✅ Reliability Benefits
- **No Redis dependency** (eliminates single point of failure)
- **Automatic scaling** (Cloud Scheduler is fully managed)
- **Better error handling** (HTTP status codes)

## Alternative: Celery-Based Deployment

For applications requiring real-time event processing or complex task dependencies, a Celery-based deployment is available in `deployment/alternatives/celery-variant/`.

### When to Use Celery Alternative
- Heavy real-time event processing requirements
- Complex task dependencies and workflows
- Need for advanced queue management features

### Migration from Celery
If migrating from an existing Celery deployment:
1. Deploy new endpoints alongside existing Celery tasks
2. Test scheduler jobs in parallel with Celery
3. Switch traffic to scheduler endpoints
4. Remove Celery infrastructure after validation

## Support and Maintenance

- **Documentation**: Keep this deployment guide updated
- **Monitoring**: Review metrics and logs weekly
- **Updates**: Update dependencies and base images monthly
- **Backups**: Verify backup procedures quarterly
- **Security**: Review and rotate secrets quarterly

## Next Steps

After successful deployment:
1. Set up custom domain with SSL
2. Configure monitoring dashboards
3. Implement CI/CD pipeline
4. Optimize based on usage patterns

For additional support, refer to:
- [Google Cloud Run Documentation](https://cloud.google.com/run/docs)
- [Cloud SQL Documentation](https://cloud.google.com/sql/docs)
- [Cloud Scheduler Documentation](https://cloud.google.com/scheduler/docs)
