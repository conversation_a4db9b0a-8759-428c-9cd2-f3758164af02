# News crawler container
FROM newsmonitor-base:latest

ENV DEPLOYMENT_ENVIRONMENT=cloud

# Switch to app user
USER appuser

# Working directory for crawler
WORKDIR /app/news_crawler

# Health check for crawler (check if process is running)
HEALTHCHECK --interval=60s --timeout=30s --start-period=10s --retries=3 \
    CMD python -c "import sys; sys.exit(0)" || exit 1

# Default command - run all spiders
CMD ["python", "run_spiders.py", "--log-level", "INFO"]
