"""
API for fetching price data from Yahoo Finance.

This module provides a unified interface for fetching price data from Yahoo Finance.
It supports fetching data for a specific ticker, date range, and interval.
The fetched data is cached in the database for future use.
"""

import pprint
import time
from datetime import date, datetime, timedelta, timezone
import traceback
from typing import Any, Dict, Optional, Union

import pandas as pd
import exchange_calendars as ecals
import yfinance as yf

from db.database import get_db_manager
from utils.logging_config import get_api_logger

logger = get_api_logger(__name__)


class YahooFinanceAPI:
    def __init__(
        self,
        max_retries: int = 3,
        retry_delay: int = 5
    ):
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.db = get_db_manager().market_data_service
        logger.info("Initialized YahooFinanceAPI")
    
    def _is_trading_day(self, date: date, code: str = "XNYS") -> bool:
        exchange_calendar = ecals.get_calendar(code)
        return exchange_calendar.is_session(date)
    
    def get_latest_price(self, ticker: str) -> pd.DataFrame:
        yf_ticker = yf.tickers.Ticker(ticker)
        df = yf_ticker.history(period="1d")
        if df.empty:
            return pd.DataFrame()
        return df

    def get_price_data(
        self,
        ticker: str,
        start_date: Optional[Union[str, datetime]] = None,
        end_date: Optional[Union[str, datetime]] = None,
        period: Optional[str] = None,
        interval: str = "1d",
        force_refresh: bool = False
    ) -> pd.DataFrame:
        logger.info(f"Getting price data for {ticker}, interval={interval}, "
                    f"start={start_date}, end={end_date}, period={period}, force_refresh={force_refresh}")
        if not ticker:
            logger.error("Ticker symbol is required")
            return pd.DataFrame()
        
        today = datetime.now().date()
        if period:
            period_days = int(period[:-1])
            start_date = today - timedelta(days=period_days) if period_days >=3 else today - timedelta(days=3)
            end_date = today
        else:
            start_date = datetime.strptime(start_date, '%Y-%m-%d') if isinstance(
                start_date, str) else start_date
            start_date = start_date.date() if start_date else today
            if start_date > today:
                start_date = today
                logger.warning(f"Start date {start_date} is in the future, setting to today")
            end_date = datetime.strptime(end_date, '%Y-%m-%d') if isinstance(
                end_date, str) else end_date
            end_date = end_date.date() if end_date else today

        if force_refresh:
            logger.info(f"Force refresh enabled, fetching all data for {ticker}")
            self._fetch_all_data_to_db(ticker, start_date, end_date, interval)
            return self.db.get_market_data(ticker, interval, start_date, end_date)

        data = self._load_from_db_and_fetch_delta(
            ticker, start_date, end_date, interval)
        if data.empty:
            logger.warning(f"No data returned for {ticker} from {start_date} to {end_date}")
            return pd.DataFrame()
        
        if period:
            while period_days <= 3:
                period_start = today - timedelta(days=period_days-1)
                filtered_data = data[period_start.strftime('%Y-%m-%d'):today.strftime('%Y-%m-%d')]
                if not filtered_data.empty:
                    data = filtered_data
                    break
                logger.debug(f"No data returned for {ticker} from {period_start} to {today}, "
                            f"expanding period to {period_days} days")
                period_days += 1
        
        logger.info(
            f"Returning cached + delta data for {ticker} ({len(data)} rows)")
        return data


    def _load_from_db_and_fetch_delta(
        self,
        ticker: str,
        start_date: date,
        end_date: date,
        interval: str,
    ) -> pd.DataFrame:
        try:
            data = self.db.get_market_data(
                ticker, interval, start_date, end_date)

            logger.info(
                f"Loaded {len(data)} rows from DB for {ticker} ({interval})")
            now = pd.Timestamp.now()
            fetch_dates = set()
            stored_dates = set([idx.date() for idx in data.index]) if not data.empty else set()
            for i in range((end_date - start_date).days + 1):
                date = start_date + timedelta(days=i)
                if date > now.date() or not self._is_trading_day(date):
                    logger.debug(f"Date {date} is in the future or not a trading day, skipping")
                    continue
                if date not in stored_dates or date == now.date():
                    fetch_dates.add(date)
            
            if fetch_dates:
                delta_start_date, delta_end_date = min(fetch_dates), max(fetch_dates)
                logger.debug(f"Found {len(fetch_dates)} dates to fetch for {ticker}, "
                            f"Min: {delta_start_date}, Max: {delta_end_date}, fetching delta")
                fetched_data = self._fetch_data(
                    ticker, delta_start_date, delta_end_date, interval=interval)
                if not fetched_data.empty:
                    self.db.upsert_market_data(ticker, interval, fetched_data)
                else:
                    logger.warning(f"Failed to fetch missing data for {ticker}")

            # Reload data for the full desired range
            full_data = self.db.get_market_data(
                ticker, interval, start_date, end_date)
            logger.info(f"Returning {len(full_data)} rows after delta update, start_date={start_date}, end_date={end_date}")
            return full_data

        except Exception as e:
            logger.error(f"Error in _load_from_db_and_fetch_delta: {e}")
            print(traceback.format_exc())
            return pd.DataFrame()

    def _fetch_all_data_to_db(
        self,
        ticker: str,
        start_date: date,
        end_date: date,
        interval: str
    ) -> pd.DataFrame:
        logger.info(
            f"Fetching full dataset for {ticker}, start={start_date}, end={end_date}")
        df = self._fetch_data(ticker, start_date, end_date, interval=interval)

        if not df.empty:
            logger.info(f"Fetched {len(df)} rows for {ticker}, saving to DB")
            logger.debug(f"Sample data:\n{df.head()}")
            self.db.upsert_market_data(ticker, interval, df)
        else:
            logger.warning(f"No data fetched for {ticker} from Yahoo")

        return df

    def _fetch_data(
        self,
        ticker: str,
        start_date: date,
        end_date: date,
        interval: str
    ) -> pd.DataFrame:
        # Add one day to end_date to include the end_date in the range
        end_str = (end_date + timedelta(days=1)).strftime('%Y-%m-%d') if end_date else None
        start_str = start_date.strftime('%Y-%m-%d') if start_date else None
        yf_ticker = yf.Ticker(ticker)

        for attempt in range(1, self.max_retries + 1):
            try:
                logger.info(
                    f"Downloading data for {ticker} from {start_str} to {end_str}")
                df = yf_ticker.history(start=start_str, end=end_str, interval=interval)

                if df.empty:
                    logger.warning(f"No data received from Yahoo for {ticker}")
                    return pd.DataFrame()                
                return df

            except Exception as e:
                logger.warning(
                    f"[{attempt}/{self.max_retries}] Error fetching data for {ticker}: {e}")
                if attempt < self.max_retries:
                    logger.info(f"Retrying in {self.retry_delay} seconds...")
                    time.sleep(self.retry_delay)
                else:
                    logger.error(
                        f"Max retries reached for {ticker}. Failing fetch.")
                    return pd.DataFrame()
    
yahoo_api = YahooFinanceAPI()


def main():
    data = yahoo_api.get_price_data(
        ticker='SPY',
        interval='1m',
        period='1d',
        # start_date='2025-06-13',
        # end_date='2025-06-20',
        force_refresh=False
    )
    # data = yahoo_api.get_latest_price("SPY")
    pprint.pprint(data.tail().reset_index().to_dict(orient='dict'), indent=2)

if __name__ == "__main__":
    main()
