from io import BytesIO
import json
import os
import time
from datetime import datetime, timezone
from typing import List, Optional

from openai import OpenAI

from apis.llm.base import BaseAPIManager
# Import logging configuration
from apis.llm.data_types import BatchResponse, BatchStatus, CompletionRequest, CompletionResponse, CompletionStatus
from utils.logging_config import configure_logging

# Configure logger for this module
logger = configure_logging(__name__, log_file='openai.log')


class OpenAIManager(BaseAPIManager):
    """OpenAI API implementation."""

    def __init__(self, total_budget: float = 0.0, total_cost: float = 0.0, requests_per_minute: int = 30, **kwargs):
        model_pricing = {
            'gpt-4.1': {
                'input_rate': 0.002,  # $2 per 1M tokens
                'output_rate': 0.008  # $8 per 1M tokens
            },
            'gpt-4.1-mini': {
                'input_rate': 0.0004,  # $0.4 per 1M tokens
                'output_rate': 0.0016  # $1.6 per 1M tokens
            },
            'gpt-4.1-nano': {
                'input_rate': 0.0001,  # $0.1 per 1M tokens
                'output_rate': 0.0004  # $0.4 per 1M tokens
            },
            'o4-mini': {
                'input_rate': 0.0011,  # $1.1 per 1M tokens
                'output_rate': 0.0044  # $4.4 per 1M tokens
            },
            'o3': {
                'input_rate': 0.002,  # $2 per 1M tokens
                'output_rate': 0.008  # $8 per 1M tokens
            }
        }
        self.reasoning_models = ['o4-mini', 'o3']
        super().__init__(total_budget=total_budget,
                         total_cost=total_cost,
                         requests_per_minute=requests_per_minute,
                         model_pricing=model_pricing,
                         batch_discount=0.5,
                         **kwargs)
        self.client = self._initialize_client()

    def _initialize_client(self):
        return OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

    def _make_completion_request(self, request: CompletionRequest) -> CompletionResponse:
        messages = []
        if request.system_prompt:
            messages.append(
                {"role": "system", "content": request.system_prompt})
        messages.append({"role": "user", "content": request.user_prompt})

        if request.model in self.reasoning_models:
            response = self.client.chat.completions.create(
                model=request.model,
                messages=messages,
                max_completion_tokens=request.max_tokens,
                reasoning_effort="medium"
            )
        else:
            response = self.client.chat.completions.create(
                model=request.model,
                messages=messages,
                max_tokens=request.max_tokens,
                temperature=request.temperature
            )

        return CompletionResponse(
            content=response.choices[0].message.content,
            status=CompletionStatus.SUCCEEDED.value,
            model=response.model,
            input_tokens=response.usage.prompt_tokens,
            output_tokens=response.usage.completion_tokens,
            raw_response=response.to_json()
        )

    def _create_batch_request(self, requests: List[CompletionRequest]) -> BatchResponse:
        api_requests = []
        for request in requests:
            messages = []
            if request.system_prompt:
                messages.append(
                    {"role": "system", "content": request.system_prompt})
            messages.append({"role": "user", "content": request.user_prompt})

            api_request_data = {
                "custom_id": request.custom_id,
                "method": "POST",
                "url": "/v1/chat/completions",
                "body": {
                    "model": request.model,
                    "max_tokens": request.max_tokens,
                    "messages": messages,
                    "temperature": request.temperature
                }
            }
            api_requests.append(api_request_data)

        from cloud.cloud_config import get_cloud_config
        cloud_config = get_cloud_config()

        if cloud_config.is_cloud_environment:
            # CLOUD ENVIRONMENT: Use in-memory storage
            jsonl_content = ""
            for request in api_requests:
                jsonl_content += json.dumps(request) + '\n'
            
            # Create a file-like object
            file_like_object = BytesIO(jsonl_content.encode('utf-8'))
            file_like_object.name = f"batch_requests_{int(time.time())}.jsonl"
            
            # Upload to OpenAI
            batch_input_file = self.client.files.create(
                file=file_like_object,
                purpose="batch"
            )
        else:
            # LOCAL ENVIRONMENT: Use file system
            batch_file_name = f"batch_requests_{int(time.time())}.jsonl"
            with open(batch_file_name, 'w') as f:
                for request in api_requests:
                    f.write(json.dumps(request) + '\n')

            # Upload the file
            with open(batch_file_name, 'rb') as f:
                batch_input_file = self.client.files.create(
                    file=f, purpose="batch")

        # Create the batch
        response = self.client.batches.create(
            input_file_id=batch_input_file.id,
            endpoint="/v1/chat/completions",
            completion_window="24h"
        )

        # Clean up the temporary file
        os.remove(batch_file_name)

        return BatchResponse(
            id=response.id,
            status=response.status,
            created_at=datetime.fromtimestamp(
                response.created_at, tz=timezone.utc),
            expires_at=datetime.fromtimestamp(
                response.expires_at, tz=timezone.utc),
            raw_response=response.to_json(),
        )

    def _retrieve_batch_status_request(self, batch_id: str) -> BatchResponse:
        response = self.client.batches.retrieve(batch_id)

        return BatchResponse(
            id=response.id,
            status=response.status,
            created_at=datetime.fromtimestamp(
                response.created_at, tz=timezone.utc),
            expires_at=datetime.fromtimestamp(
                response.expires_at, tz=timezone.utc),
            completed_at=datetime.fromtimestamp(
                response.completed_at, tz=timezone.utc) if response.completed_at else None,
            raw_response=response.to_json()
        )

    def _retrieve_batch_results_request(self, batch_id: str) -> List[CompletionResponse]:
        # First get the batch status to get the output file ID
        batch = self.client.batches.retrieve(batch_id)

        if batch.status != BatchStatus.COMPLETED.value or not batch.output_file_id:
            logger.warning(
                f"Batch {batch_id} is not completed or has no output file")
            return []

        # Download the results file
        file_response = self.client.files.content(batch.output_file_id)

        results = []
        for line in file_response.text.strip().split('\n'):
            if not line:
                continue
            result = json.loads(line)

            if result['response']:
                body = result['response']['body']
                status = CompletionStatus.SUCCEEDED.value
                results.append(CompletionResponse(
                    content=body['choices'][0]['message']['content'],
                    status=status,
                    model=body['model'],
                    input_tokens=body['usage']['prompt_tokens'],
                    output_tokens=body['usage']['completion_tokens'],
                    raw_response=line,
                    custom_id=result['custom_id']
                ))
            else:
                status = CompletionStatus.FAILED.value
                results.append(CompletionResponse(
                    content="",
                    status=status,
                    model="",
                    input_tokens=0,
                    output_tokens=0,
                    raw_response=line,
                    custom_id=result['custom_id']
                ))

        return results

    def _list_batches_request(self, limit: int) -> List[BatchResponse]:
        """List all Message Batches."""
        response = self.client.batches.list(limit=limit)
        batches = []
        for response_line in response.data:
            batches.append(BatchResponse(
                id=response_line.id,
                status=response_line.status,
                created_at=datetime.fromtimestamp(
                    response_line.created_at, tz=timezone.utc),
                expires_at=datetime.fromtimestamp(
                    response_line.expires_at, tz=timezone.utc),
                completed_at=datetime.fromtimestamp(
                    response_line.completed_at, tz=timezone.utc) if response_line.completed_at else None,
                raw_response=response_line.to_json()
            ))
        return batches
