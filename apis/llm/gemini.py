import json
import os
import time
import uuid
from typing import List

from google import genai
from google.genai.types import CreateBatchJobConfig, JobState, HttpOptions, BatchJob
from google.cloud import storage
import google.auth
from google.auth.credentials import Credentials

from apis.llm.base import BaseAPIManager
from apis.llm.data_types import BatchResponse, BatchStatus, CompletionRequest, CompletionResponse, CompletionStatus
from utils.logging_config import configure_logging

# Configure logger for this module
logger = configure_logging(__name__, log_file='gemini.log')


class GeminiManager(BaseAPIManager):
    """Google Gemini API implementation."""

    def __init__(self, total_budget: float = 0.0, total_cost: float = 0.0, requests_per_minute: int = 30,
                 bucket_name: str = None, use_vertex_ai: bool = True, **kwargs):
        model_pricing = {
            'gemini-2.5-pro': {
                'input_rate': 0.00125,  # $1.25 per 1M tokens
                'output_rate': 0.025  # $2.50 per 1M tokens
            },
            'gemini-2.5-flash': {
                'input_rate': 0.00015,  # $0.15 per 1M tokens
                'output_rate': 0.0006  # $0.60 per 1M tokens
            },
            'gemini-2.0-flash': {
                'input_rate': 0.00015,  # $0.15 per 1M tokens
                'output_rate': 0.0006  # $0.60 per 1M tokens
            }
        }
        super().__init__(total_budget=total_budget,
                         total_cost=total_cost,
                         requests_per_minute=requests_per_minute,
                         model_pricing=model_pricing,
                         batch_discount=0.5,  # 50% discount for batch requests
                         **kwargs)

        self.bucket_name = bucket_name or os.getenv(
            "GOOGLE_CLOUD_STORAGE_BUCKET")
        self.use_vertex_ai = use_vertex_ai

        self.storage_client = None
        self.client = self._initialize_client()

    def _initialize_client(self):
        """Initialize the Gemini client."""
        if self.use_vertex_ai:
            # Get credentials
            credentials, project = google.auth.default(
                scopes=['https://www.googleapis.com/auth/cloud-platform']
            )

            # Setup for Vertex AI
            project = project or os.getenv('GOOGLE_CLOUD_PROJECT')
            location = os.getenv('VERTEX_AI_LOCATION', 'us-central1')

            if not project or not location:
                raise ValueError(
                    "Missing environment variables for Vertex AI")

            # Use v1 API for production stability
            client = genai.Client(
                vertexai=True,
                project=project,
                location=location,
                credentials=credentials,
                http_options=HttpOptions(api_version="v1")
            )
        else:
            # Setup for Gemini Developer API
            api_key = os.getenv("GEMINI_API_KEY")
            if not api_key:
                raise ValueError("GEMINI_API_KEY environment variable not set")

            client = genai.Client(
                api_key=api_key,
                http_options=HttpOptions(api_version="v1")
            )

        return client

    def _initialize_storage_client(self, credentials: Credentials):
        """Initialize Google Cloud Storage client for batch operations."""
        try:
            return storage.Client(credentials=credentials)
        except Exception as e:
            logger.warning(f"Failed to initialize Storage client: {str(e)}")
            return None

    def _make_completion_request(self, request: CompletionRequest) -> CompletionResponse:
        """Make a single completion request to Gemini."""
        from google.genai import types

        # Prepare the content for the new SDK
        config_params = {
            "max_output_tokens": request.max_tokens,
            "temperature": request.temperature
        }

        if request.system_prompt:
            config_params["system_instruction"] = request.system_prompt

        config = types.GenerateContentConfig(**config_params)

        # Make the request using the new SDK
        response = self.client.models.generate_content(
            model=request.model,
            contents=request.user_prompt,
            config=config
        )

        # Extract response content using the text property
        content = response.text

        # Get token usage
        input_tokens = response.usage_metadata.prompt_token_count
        output_tokens = response.usage_metadata.candidates_token_count

        return CompletionResponse(
            content=content,
            status=CompletionStatus.SUCCEEDED.value,
            model=response.model_version,
            input_tokens=input_tokens,
            output_tokens=output_tokens,
            raw_response=str(response)
        )

    def _upload_batch_file_to_gcs(self, requests: List[CompletionRequest]) -> str:
        """Upload batch requests JSONL file to Google Cloud Storage."""
        if not self.storage_client or not self.bucket_name:
            raise ValueError(
                "Google Cloud Storage not configured. Set GOOGLE_CLOUD_STORAGE_BUCKET environment variable.")

        # Generate unique filename
        timestamp = int(time.time())
        filename = f"batch_requests/{timestamp}_{uuid.uuid4().hex[:8]}.jsonl"

        # Create JSONL content
        jsonl_content = []
        for i, request in enumerate(requests):
            # Convert CompletionRequest to Gemini batch format
            batch_request = {
                "request": {
                    "contents": [
                        {
                            "role": "user",
                            "parts": [{"text": request.user_prompt}]
                        }
                    ],
                    "generationConfig": {
                        "maxOutputTokens": request.max_tokens,
                        "temperature": request.temperature
                    }
                }
            }

            # Add system instruction if present
            if request.system_prompt:
                batch_request["request"]["systemInstruction"] = {
                    "parts": [{"text": request.system_prompt}]
                }

            # Add custom_id or generate one
            custom_id = request.custom_id or f"request_{i}"
            batch_request["custom_id"] = custom_id

            jsonl_content.append(json.dumps(batch_request))

        # Upload to GCS
        try:
            bucket = self.storage_client.bucket(self.bucket_name)
            blob = bucket.blob(filename)
            blob.upload_from_string(
                '\n'.join(jsonl_content), content_type='application/jsonl')

            gcs_uri = f"gs://{self.bucket_name}/{filename}"
            logger.info(f"Uploaded batch file to {gcs_uri}")
            return gcs_uri

        except Exception as e:
            logger.error(f"Failed to upload batch file to GCS: {str(e)}")
            raise

    def _create_batch_request(self, requests: List[CompletionRequest]) -> BatchResponse:
        """Create a real batch request using Google GenAI native batch API."""
        try:
            # Upload batch requests to Cloud Storage
            input_uri = self._upload_batch_file_to_gcs(requests)

            # Generate output URI
            timestamp = int(time.time())
            output_uri = f"gs://{self.bucket_name}/batch_results/{timestamp}_{uuid.uuid4().hex[:8]}"

            # Create batch job using the real API
            job = self.client.batches.create(
                model=requests[0].model,  # Use model from first request
                src=input_uri,
                config=CreateBatchJobConfig(dest=output_uri)
            )

            logger.info(
                f"Created batch job: {job.name} with status: {job.state}")

            return BatchResponse(
                id=job.name,
                status=self._convert_job_state_to_batch_status(job.state),
                created_at=job.create_time,
                raw_response=str(job)
            )

        except Exception as e:
            logger.error(f"Failed to create batch request: {str(e)}")
            raise

    def _convert_job_state_to_batch_status(self, job_state: JobState) -> str:
        """Convert Google's JobState to our BatchStatus."""
        state_mapping = {
            JobState.JOB_STATE_PENDING: BatchStatus.IN_PROGRESS.value,
            JobState.JOB_STATE_RUNNING: BatchStatus.IN_PROGRESS.value,
            JobState.JOB_STATE_SUCCEEDED: BatchStatus.COMPLETED.value,
            JobState.JOB_STATE_FAILED: BatchStatus.FAILED.value,
            JobState.JOB_STATE_CANCELLED: BatchStatus.CANCELLED.value,
            JobState.JOB_STATE_CANCELLING: BatchStatus.CANCELLING.value
        }
        return state_mapping.get(job_state, BatchStatus.IN_PROGRESS.value)

    def _retrieve_batch_status_request(self, batch_id: str) -> BatchResponse:
        """Retrieve the status of a batch request using native API."""
        try:
            # Get batch job status
            job = self.client.batches.get(name=batch_id)

            return BatchResponse(
                id=job.name,
                status=self._convert_job_state_to_batch_status(job.state),
                created_at=job.create_time,
                completed_at=job.end_time,
                raw_response=str(job)
            )

        except Exception as e:
            logger.error(f"Failed to retrieve batch status: {str(e)}")
            raise

    def _download_batch_results_from_gcs(self, job: BatchJob) -> List[CompletionResponse]:
        """Download and parse batch results from Google Cloud Storage."""
        if not self.storage_client:
            raise ValueError("Google Cloud Storage client not available")

        try:
            output_uri = job.dest.gcs_uri
            if not output_uri:
                logger.warning(
                    "No output URI found in job, results may not be ready")
                return []

            # Parse GCS URI
            if not output_uri.startswith('gs://'):
                raise ValueError(f"Invalid GCS URI: {output_uri}")

            uri_parts = output_uri[5:].split('/', 1)  # Remove 'gs://' prefix
            bucket_name = uri_parts[0]
            blob_name = uri_parts[1] if len(uri_parts) > 1 else ""

            # Download results
            bucket = self.storage_client.bucket(bucket_name)

            # List all result files (batch results might be split into multiple files)
            results = []
            for blob in bucket.list_blobs(prefix=blob_name):
                if not blob.name.endswith('.jsonl'):
                    continue
                content = blob.download_as_text()

                # Parse JSONL results
                for line in content.strip().split('\n'):
                    if not line:
                        continue
                    result = json.loads(line)

                    # Extract response data
                    custom_id = result.get('custom_id', '')
                    response_data = result.get('response', {})

                    if response_data:
                        # Parse the response
                        candidate = response_data['candidates'][0]
                        parts = candidate['content']['parts']
                        content = ''.join(part.get('text', '')
                                          for part in parts if 'text' in part)

                        # Extract usage metadata
                        usage = response_data['usageMetadata']
                        input_tokens = usage.get('promptTokenCount', 0)
                        output_tokens = usage.get('candidatesTokenCount', 0)

                        results.append(CompletionResponse(
                            content=content,
                            status=CompletionStatus.SUCCEEDED.value,
                            model=response_data['modelVersion'],
                            input_tokens=input_tokens,
                            output_tokens=output_tokens,
                            raw_response=line,
                            custom_id=custom_id
                        ))
                    else:
                        logger.warning(
                            f"No response data found in result: {result}")
                        results.append(CompletionResponse(
                            content="",
                            status=CompletionStatus.FAILED.value,
                            model="",
                            input_tokens=0,
                            output_tokens=0,
                            raw_response=line,
                            custom_id=custom_id
                        ))

            return results

        except Exception as e:
            logger.error(f"Failed to download batch results: {str(e)}")
            return []

    def _retrieve_batch_results_request(self, batch_id: str) -> List[CompletionResponse]:
        """Retrieve the results of a completed batch request using native API."""
        try:
            # Get batch job
            job = self.client.batches.get(name=batch_id)

            # Check if batch is completed
            if job.state != JobState.JOB_STATE_SUCCEEDED:
                raise ValueError(
                    f"Batch {batch_id} is not completed successfully. Status: {job.state}")

            # Download and parse results from GCS
            return self._download_batch_results_from_gcs(job)

        except Exception as e:
            logger.error(f"Failed to retrieve batch results: {str(e)}")
            return []

    def _list_batches_request(self, limit: int) -> List[BatchResponse]:
        """List all batch requests using native API."""
        try:
            # Use the real API to list batches
            batches = self.client.batches.list(limit=limit)

            batch_responses = []
            for job in batches:
                batch_responses.append(BatchResponse(
                    id=job.name,
                    status=self._convert_job_state_to_batch_status(job.state),
                    created_at=job.create_time,
                    completed_at=job.end_time,
                    raw_response=str(job)
                ))

            return batch_responses

        except Exception as e:
            logger.error(f"Failed to list batches: {str(e)}")
            return []

    def cancel_batch(self, batch_id: str) -> bool:
        """Cancel a batch request."""
        try:
            self.client.batches.cancel(name=batch_id)
            logger.info(f"Cancelled batch job: {batch_id}")
            return True
        except Exception as e:
            logger.error(f"Failed to cancel batch: {str(e)}")
            return False

    def delete_batch(self, batch_id: str) -> bool:
        """Delete a batch job."""
        try:
            self.client.batches.delete(name=batch_id)
            logger.info(f"Deleted batch job: {batch_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to delete batch: {str(e)}")
            return False
