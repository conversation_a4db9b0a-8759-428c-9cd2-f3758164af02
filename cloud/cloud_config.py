"""
Cloud-specific configuration for NewsMonitor application.
This module provides configuration overrides for cloud deployment.
"""

import os
import logging
from pathlib import Path
import json

# Configure logging for cloud environment
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

class CloudConfig:
    """Configuration class for cloud deployment."""
    
    def __init__(self):
        self.project_id = os.getenv('GOOGLE_CLOUD_PROJECT')
        self.region = os.getenv('GOOGLE_CLOUD_LOCATION', 'us-central1')
        self.is_cloud_environment = os.getenv('DEPLOYMENT_ENVIRONMENT', '') == 'cloud'
                                    
    def get_log_config(self) -> dict:
        """Get logging configuration for cloud environment."""
        if self.is_cloud_environment:
            return {
                "version": 1,
                "disable_existing_loggers": False,
                "formatters": {
                    "cloud": {
                        "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
                    }
                },
                "handlers": {
                    "console": {
                        "class": "logging.StreamHandler",
                        "formatter": "cloud",
                        "stream": "ext://sys.stdout"
                    }
                },
                "root": {
                    "level": "INFO",
                    "handlers": ["console"]
                }
            }
        else:
            # Return local logging config
            return {}

# Global cloud config instance
cloud_config = CloudConfig()

def get_cloud_config() -> CloudConfig:
    """Get the global cloud configuration instance."""
    return cloud_config

def is_cloud_environment() -> bool:
    """Check if running in cloud environment."""
    return cloud_config.is_cloud_environment