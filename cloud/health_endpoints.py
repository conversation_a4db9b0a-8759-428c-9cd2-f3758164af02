"""
Health check endpoints for NewsMonitor application.
These endpoints are used by Cloud Run health checks and monitoring.
"""

import os
import sys
import json
import time
import traceback
from datetime import datetime, timezone
from sqlalchemy import text
from flask import Blueprint, jsonify, request
from typing import Dict, Any

# Add the project root to the Python path
sys.path.insert(0, '/app')

health_bp = Blueprint('health', __name__)

def check_database_health() -> Dict[str, Any]:
    """Check database connectivity and basic operations."""
    try:
        from db.database import get_db_manager
        
        start_time = time.time()
        db_manager = get_db_manager()
        
        # Test basic database connectivity
        with db_manager.connection.get_session() as session:
            result = session.execute(text("SELECT 1 as test")).fetchone()
            if result and result[0] == 1:
                response_time = (time.time() - start_time) * 1000
                return {
                    "status": "healthy",
                    "response_time_ms": round(response_time, 2),
                    "message": "Database connection successful"
                }
            else:
                return {
                    "status": "unhealthy",
                    "message": "Database query returned unexpected result"
                }
    except Exception as e:
        return {
            "status": "unhealthy",
            "message": f"Database connection failed: {str(e)}"
        }

def check_redis_health() -> Dict[str, Any]:
    """Check Redis connectivity for Celery."""
    try:
        import redis
        redis_ip = os.getenv('REDIS_IP', 'localhost')
        redis_url = f"redis://{redis_ip}:6379/0"
        
        start_time = time.time()
        r = redis.from_url(redis_url, socket_timeout=5)
        r.ping()
        response_time = (time.time() - start_time) * 1000
        
        return {
            "status": "healthy",
            "response_time_ms": round(response_time, 2),
            "message": "Redis connection successful"
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "message": f"Redis connection failed: {str(e)}"
        }

def check_celery_health() -> Dict[str, Any]:
    """Check Celery worker status."""
    try:
        from celery import Celery
        redis_ip = os.getenv('REDIS_IP', 'localhost')
        redis_url = f"redis://{redis_ip}:6379/0"
        
        celery_app = Celery('health_check', broker=redis_url)
        
        # Check if workers are available
        inspect = celery_app.control.inspect()
        stats = inspect.stats()
        
        if stats:
            active_workers = len(stats)
            return {
                "status": "healthy",
                "active_workers": active_workers,
                "message": f"{active_workers} Celery workers active"
            }
        else:
            return {
                "status": "unhealthy",
                "message": "No Celery workers found"
            }
    except Exception as e:
        return {
            "status": "unhealthy",
            "message": f"Celery check failed: {str(e)}"
        }

def check_llm_apis_health() -> Dict[str, Any]:
    """Check LLM API connectivity."""
    try:
        from cloud.cloud_config import get_cloud_config
        
        config = get_cloud_config()
        api_keys = {
            "openai-key": os.getenv("OPENAI_API_KEY", ""),
            "anthropic-key": os.getenv("ANTHROPIC_API_KEY", ""),
            "gemini-key": os.getenv("GEMINI_API_KEY", "")
        }
        
        api_status = {}
        overall_healthy = True
        
        # Check each API key is present
        for api_name, key in api_keys.items():
            if key and len(key) > 10:  # Basic validation
                api_status[api_name] = "configured"
            else:
                api_status[api_name] = "missing"
                overall_healthy = False
        
        return {
            "status": "healthy" if overall_healthy else "degraded",
            "apis": api_status,
            "message": "LLM API keys checked"
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "message": f"LLM API check failed: {str(e)}"
        }

def check_environment_health() -> Dict[str, Any]:
    """Check required environment variables and configuration."""
    try:
        from cloud.cloud_config import get_cloud_config, is_cloud_environment
        
        config = get_cloud_config()
        
        required_vars = [
            'GOOGLE_CLOUD_PROJECT',
            'DATABASE_URL',
            'SECRET_KEY'
        ]
        
        missing_vars = []
        for var in required_vars:
            if var == 'DATABASE_URL':
                value = os.getenv("DATABASE_URL", "")
            elif var == 'SECRET_KEY':
                value = os.getenv("SECRET_KEY", "dev-secret-key")
            else:
                value = os.getenv(var, '')
            
            if not value:
                missing_vars.append(var)
        
        if missing_vars:
            return {
                "status": "unhealthy",
                "missing_variables": missing_vars,
                "message": f"Missing required environment variables: {', '.join(missing_vars)}"
            }
        
        return {
            "status": "healthy",
            "environment": "cloud" if is_cloud_environment() else "local",
            "project_id": config.project_id,
            "region": config.region,
            "message": "Environment configuration valid"
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "message": f"Environment check failed: {str(e)}"
        }

@health_bp.route('/health')
def health_check():
    """Main health check endpoint."""
    try:
        service_type = os.getenv('SERVICE_TYPE', 'web')
        
        health_data = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "service": f"newsmonitor-{service_type}",
            "version": os.getenv('SERVICE_VERSION', 'unknown'),
            "environment": "cloud" if os.getenv('GOOGLE_CLOUD_PROJECT') else "local",
            "checks": {}
        }
        
        # Always check environment
        health_data["checks"]["environment"] = check_environment_health()
        
        # Service-specific health checks
        if service_type == 'web':
            health_data["checks"]["database"] = check_database_health()
            health_data["checks"]["llm_apis"] = check_llm_apis_health()
            if not os.getenv('DISABLE_CELERY', '').lower() == 'true':
                health_data["checks"]["redis"] = check_redis_health()
        
        elif service_type == 'celery':
            health_data["checks"]["database"] = check_database_health()
            health_data["checks"]["redis"] = check_redis_health()
            health_data["checks"]["celery"] = check_celery_health()
        
        elif service_type == 'crawler':
            health_data["checks"]["database"] = check_database_health()
        
        # Determine overall health
        all_checks = health_data["checks"].values()
        healthy_count = sum(1 for check in all_checks if check["status"] == "healthy")
        degraded_count = sum(1 for check in all_checks if check["status"] == "degraded")
        total_checks = len(all_checks)
        
        if healthy_count == total_checks:
            health_data["status"] = "healthy"
            status_code = 200
        elif healthy_count + degraded_count == total_checks:
            health_data["status"] = "degraded"
            status_code = 200
        else:
            health_data["status"] = "unhealthy"
            status_code = 503
        
        return jsonify(health_data), status_code
        
    except Exception as e:
        error_data = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "status": "error",
            "message": f"Health check failed: {str(e)}",
            "traceback": traceback.format_exc() if request.args.get('debug') else None
        }
        return jsonify(error_data), 500

@health_bp.route('/health/live')
def liveness_check():
    """Liveness probe endpoint - basic service availability."""
    return jsonify({
        "status": "alive",
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "service": os.getenv('SERVICE_TYPE', 'unknown')
    }), 200

@health_bp.route('/health/ready')
def readiness_check():
    """Readiness probe endpoint - service ready to handle requests."""
    try:
        service_type = os.getenv('SERVICE_TYPE', 'web')
        
        # Basic readiness checks
        checks = {}
        
        if service_type == 'web':
            checks["database"] = check_database_health()
        elif service_type == 'celery':
            checks["redis"] = check_redis_health()
        elif service_type == 'crawler':
            checks["database"] = check_database_health()
        
        # Check if all critical services are ready
        all_ready = all(check["status"] in ["healthy", "degraded"] for check in checks.values())
        
        if all_ready:
            return jsonify({
                "status": "ready",
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "checks": checks
            }), 200
        else:
            return jsonify({
                "status": "not_ready",
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "checks": checks
            }), 503
            
    except Exception as e:
        return jsonify({
            "status": "error",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "message": str(e)
        }), 500

@health_bp.route('/health/startup')
def startup_check():
    """Startup probe endpoint - service has started successfully."""
    return jsonify({
        "status": "started",
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "service": os.getenv('SERVICE_TYPE', 'unknown'),
        "uptime": time.time() - float(os.getenv('START_TIME', time.time()))
    }), 200
