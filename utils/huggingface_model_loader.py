import logging
import torch
from threading import Lock
from typing import TYPE_CHECKING, Dict, Optional, Tuple, Union, Any

from transformers import (
    AutoTokenizer,
    AutoModel,
    AutoModelForSequenceClassification,
    AutoModelForCausalLM,
    AutoModelForTokenClassification,
    AutoModelForMaskedLM,
)

if TYPE_CHECKING:
    from sentence_transformers import SentenceTransformer
    from peft import PeftModel

try:
    from sentence_transformers import SentenceTransformer
    has_sentence_transformers = True
except ImportError:
    has_sentence_transformers = False

try:
    from peft import PeftModel
    has_peft = True
except ImportError:
    has_peft = False

logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)

MODEL_TYPE_MAP = {
    "base": AutoModel,
    "sequence_classification": AutoModelForSequenceClassification,
    "causal_lm": AutoModelForCausalLM,
    "token_classification": AutoModelForTokenClassification,
    "masked_lm": AutoModelForMaskedLM,
    "sentence_transformer": None  # handled separately
}


class ModelLoader:
    _instance = None
    _lock = Lock()

    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(ModelLoader, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        # Use hasattr to avoid reinitializing on subsequent calls
        if not hasattr(self, '_model_cache'):
            self._model_cache: Dict[str, Any] = {}

    def load(
        self,
        model_name: str,
        model_type: str = "base",
        cache_dir: Optional[str] = None,
        trust_remote_code: bool = False,
        use_auth_token: Optional[Union[str, bool]] = None,
        peft_model_path: Optional[str] = None,
        device: Optional[str] = None,
        use_device_map_auto: bool = False,
        **model_kwargs,
    ) -> Union[Tuple[Any, Any], SentenceTransformer]:
        """
        Load and cache a HF or SentenceTransformer model with optional PEFT, GPU, and device_map=auto.
        """
        cache_key = f"{model_type}:{model_name}:{peft_model_path or 'no_peft'}:{use_device_map_auto}"
        device = device or ("cuda" if torch.cuda.is_available() else "cpu")

        if cache_key in self._model_cache:
            logger.info(f"Returning cached model: {cache_key}")
            return self._model_cache[cache_key]

        logger.info(f"Loading model type='{model_type}' from: {model_name}")

        if model_type == "sentence_transformer":
            if not has_sentence_transformers:
                raise ImportError(
                    "Install `sentence-transformers` to use this model type.")
            model = SentenceTransformer(model_name, cache_folder=cache_dir)
            self._model_cache[cache_key] = model
            return model

        if model_type not in MODEL_TYPE_MAP or MODEL_TYPE_MAP[model_type] is None:
            raise ValueError(
                f"Unsupported model_type '{model_type}'. Supported types: {list(MODEL_TYPE_MAP.keys())}")

        tokenizer = AutoTokenizer.from_pretrained(
            model_name,
            cache_dir=cache_dir,
            trust_remote_code=trust_remote_code,
            use_auth_token=use_auth_token,
        )

        model_class = MODEL_TYPE_MAP[model_type]

        if use_device_map_auto:
            logger.info("Loading model with device_map='auto'")
            model = model_class.from_pretrained(
                model_name,
                cache_dir=cache_dir,
                trust_remote_code=trust_remote_code,
                use_auth_token=use_auth_token,
                device_map="auto",
                **model_kwargs
            )
        else:
            model = model_class.from_pretrained(
                model_name,
                cache_dir=cache_dir,
                trust_remote_code=trust_remote_code,
                use_auth_token=use_auth_token,
                **model_kwargs
            )
            model = model.to(device)
            logger.info(f"Model moved to device: {device}")

        if peft_model_path:
            if not has_peft:
                raise ImportError("Install `peft` to use PEFT adapters.")
            logger.info(f"Applying PEFT adapter from: {peft_model_path}")
            model = PeftModel.from_pretrained(model, peft_model_path)
            if not use_device_map_auto:
                model = model.to(device)

        self._model_cache[cache_key] = (model, tokenizer)
        return model, tokenizer
