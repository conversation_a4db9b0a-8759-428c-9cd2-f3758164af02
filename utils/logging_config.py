"""
Centralized logging configuration for the NewsMonitor project.

This module provides a unified logging configuration that can be used across
all components: web application, crawler, predictor, scripts, APIs, and utilities.
"""

import os
import sys
import logging
import logging.config
from pathlib import Path
from typing import Optional, Union
from cloud.cloud_config import is_cloud_environment, get_cloud_config


def configure_logging(
    name: str,
    log_level: Union[str, int] = "INFO",
    log_file: Optional[str] = None,
    component: Optional[str] = None,
    console_output: bool = True,
    file_output: bool = True
) -> logging.Logger:
    """
    Configure logging for a module with consistent formatting and behavior.

    Args:
        name: Name of the logger (usually __name__)
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL) or numeric level
        log_file: Path to log file. If None, auto-generates based on component and name
        component: Component name ('web', 'crawler', 'predictor', 'apis') for directory organization
        console_output: Whether to output to console (default: True)
        file_output: Whether to output to file (default: True)

    Returns:
        logging.Logger: Configured logger instance
    """
    # Check if running in cloud environment
    if is_cloud_environment():
        # Use cloud logging configuration
        config = get_cloud_config()
        logging.config.dictConfig(config.get_log_config())
        return logging.getLogger(name)

    # Get or create logger
    logger = logging.getLogger(name)

    # Remove existing handlers to avoid duplicates
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    # Convert string to logging level
    if isinstance(log_level, str):
        numeric_level = getattr(logging, log_level.upper(), None)
        if not isinstance(numeric_level, int):
            print(f"Invalid log level: {log_level}, defaulting to INFO")
            numeric_level = logging.INFO
    else:
        numeric_level = log_level

    # Set level for this logger
    logger.setLevel(numeric_level)

    # Prevent propagation to root logger to avoid duplicate logs
    logger.propagate = False

    # Create formatter
    formatter = logging.Formatter(
        '%(asctime)s [%(levelname)s] %(name)s: %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    # Add file handler if requested
    if file_output:
        if log_file is None:
            # Auto-generate log file name
            module_name = name.split('.')[-1] if '.' in name else name
            log_file = f"{module_name}.log"

        # Determine log file path
        if os.path.isabs(log_file):
            log_file_path = Path(log_file)
        else:
            # Import here to avoid circular imports
            from utils.path_utils import get_logs_directory
            logs_dir = get_logs_directory(component)
            log_file_path = logs_dir / log_file

        # Ensure log file directory exists
        log_file_path.parent.mkdir(parents=True, exist_ok=True)

        # Create file handler
        file_handler = logging.FileHandler(log_file_path, encoding='utf-8')
        file_handler.setFormatter(formatter)
        file_handler.setLevel(numeric_level)
        logger.addHandler(file_handler)

    # Add console handler if requested
    if console_output:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        console_handler.setLevel(numeric_level)
        logger.addHandler(console_handler)

    return logger


def setup_root_logger(log_level: str = "INFO") -> None:
    """
    Setup the root logger with basic configuration.

    This should be called once at application startup to ensure
    consistent logging behavior across all modules.

    Args:
        log_level: Default logging level for the root logger
    """
    root_logger = logging.getLogger()

    # Only configure if not already configured
    if not root_logger.handlers:
        configure_logging('root', log_level=log_level,
                          log_file='newsmonitor.log')


# Convenience functions for component-specific logging
def get_web_logger(name: str, log_file: Optional[str] = None) -> logging.Logger:
    """Get a logger configured for web components."""
    return configure_logging(name, component='web', log_file=log_file)


def get_crawler_logger(name: str, log_file: Optional[str] = None) -> logging.Logger:
    """Get a logger configured for crawler components."""
    return configure_logging(name, component='crawler', log_file=log_file)


def get_predictor_logger(name: str, log_file: Optional[str] = None) -> logging.Logger:
    """Get a logger configured for predictor components."""
    return configure_logging(name, component='predictor', log_file=log_file)


def get_api_logger(name: str, log_file: Optional[str] = None) -> logging.Logger:
    """Get a logger configured for API components."""
    return configure_logging(name, component='apis', log_file=log_file)


def get_db_logger(name: str, log_file: Optional[str] = None) -> logging.Logger:
    """Get a logger configured for database components."""
    return configure_logging(name, component='db', log_file=log_file)


def get_nlp_logger(name: str, log_file: Optional[str] = None) -> logging.Logger:
    """Get a logger configured for nlp components."""
    return configure_logging(name, component='nlp', log_file=log_file)
