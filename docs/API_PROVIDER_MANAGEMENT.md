# API Provider Management Feature

This document describes the API Provider Management feature that allows administrators to configure and manage LLM API providers and models through the web admin interface.

## Overview

The API Provider Management feature provides:

1. **Database-driven configuration** - Store API provider and model configurations in the database
2. **Admin web interface** - Manage providers and models through the admin dashboard
3. **Dynamic API selection** - Use database-stored configurations for LLM predictions
4. **Default provider management** - Set and change default API providers

## Components

### Database Model

**ApiInfo Table** (`db/models.py`)
- `id` - Primary key
- `provider_name` - API provider (openai, anthropic, gemini)
- `model_name` - Model name (gpt-4, claude-3-opus, etc.)
- `display_name` - Human-readable name for UI
- `is_active` - Whether the provider/model is active
- `is_default` - Whether this is the default provider/model
- `configuration` - JSON configuration (pricing, limits, etc.)
- `description` - Optional description
- `created_at` / `updated_at` - Timestamps

### Service Layer

**ApiInfoService** (`db/api_info_service.py`)
- `get_all_api_info()` - Get all API configurations
- `get_default_api_info()` - Get the default API configuration
- `create_api_info()` - Create new API configuration
- `update_api_info()` - Update existing configuration
- `delete_api_info()` - Delete configuration
- `set_default_api_info()` - Set default provider/model

### Web API Endpoints

**Admin API Endpoints** (`web/app.py`)
- `GET /api/admin/api-providers` - List all API providers
- `POST /api/admin/api-providers` - Create new API provider
- `PUT /api/admin/api-providers/<id>` - Update API provider
- `DELETE /api/admin/api-providers/<id>` - Delete API provider
- `POST /api/admin/api-providers/<id>/set-default` - Set as default

### Admin Interface

**Enhanced Admin Dashboard** (`web/templates/admin.html`)
- API Provider Management section
- Add new provider/model form
- Table showing all configurations
- Set default and delete actions
- Dynamic dropdown population

### Integration

**Prediction Service** (`web/data/prediction_service.py`)
- Modified to use database-stored API configurations
- Falls back to default provider if none specified
- Integrates with existing LLM prediction workflow

## Setup Instructions

### 1. Database Migration

Create the new database table:

```python
from db.database import get_db_manager
from db.models import Base

db_manager = get_db_manager()
Base.metadata.create_all(db_manager.engine)
```

### 2. Initialize Default Providers

Run the initialization script to populate default API providers:

```bash
python scripts/init_api_providers.py
```

This will create default configurations for:
- OpenAI (o3, gpt-4.1-mini)
- Anthropic (claude-opus-4, claude-3-5-haiku)
- Google Gemini (gemini-2.5-flash, gemini-2.0-flash)

### 3. Admin Access

1. Log in as an admin user
2. Navigate to `/admin`
3. Use the "API Provider Management" section

## Usage

### Adding a New API Provider/Model

1. Go to Admin Dashboard → API Provider Management
2. Select provider from dropdown
3. Enter model name (e.g., "gpt-4-turbo")
4. Add display name and description
5. Click "Add Provider/Model"

### Setting Default Provider

1. In the API providers table, find the desired provider/model
2. Click "Set Default" button
3. The provider will be marked as default and used for predictions

### Managing Configurations

- **Active/Inactive**: Toggle provider availability
- **Delete**: Remove provider configurations (except default)
- **Edit**: Update display names and descriptions

### Using in Predictions

The system automatically uses the default provider for LLM predictions. You can also specify a preferred provider:

```python
# Uses default provider from database
prediction = await get_llm_prediction()

# Uses specific provider
prediction = await get_llm_prediction(preferred_api="openai")
```

## Configuration Format

The `configuration` field stores JSON data with provider-specific settings:

```json
{
  "pricing": {
    "input_rate": 0.00075,
    "output_rate": 0.003
  },
  "max_tokens": 4096,
  "temperature": 0.1
}
```

## Security

- All API endpoints require admin authentication
- Default providers cannot be deleted
- Configuration changes are logged
- Input validation on all forms

## Testing

Run the test suite to validate the implementation:

```bash
python scripts/test_api_provider_feature.py
```

## Future Enhancements

1. **API Key Management** - Store encrypted API keys in database
2. **Usage Tracking** - Monitor API usage and costs per provider
3. **Load Balancing** - Distribute requests across multiple providers
4. **Model Performance Metrics** - Track prediction accuracy by model
5. **Batch Operations** - Bulk import/export of configurations

## Troubleshooting

### Common Issues

1. **Database Connection Errors**
   - Ensure PostgreSQL is running
   - Check database credentials in environment variables

2. **Missing Default Provider**
   - Run the initialization script
   - Manually set a default through the admin interface

3. **Permission Errors**
   - Ensure user has admin privileges
   - Check authentication middleware

### Logs

Check application logs for detailed error information:
- Database operations: `db/api_info_service.py`
- Web requests: `web/app.py`
- Predictions: `web/data/prediction_service.py`
