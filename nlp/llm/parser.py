"""
Configurable parsers for processing LLM completion responses.
Supports JSON, regex, and custom parsing strategies.
"""

import json
import re
from typing import Dict, Any, List


class ConfigurableParser:
    """Base class for configurable parsers."""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
    
    def parse(self, text: str) -> Dict[str, Any]:
        """Parse text and return structured data."""
        raise NotImplementedError


class JSONParser(ConfigurableParser):
    """JSON parser with flexible extraction options."""
    
    def parse(self, text: str) -> Dict[str, Any]:
        """Parse JSON from text with fallback strategies."""
        # Strategy 1: Direct JSON parsing
        try:
            return json.loads(text.strip())
        except json.JSONDecodeError:
            pass
        
        # Strategy 2: Extract JSON from markdown code blocks
        json_pattern = r'```(?:json)?\s*(\{.*?\})\s*```'
        matches = re.findall(json_pattern, text, re.DOTALL | re.IGNORECASE)
        for match in matches:
            try:
                return json.loads(match)
            except json.JSONDecodeError:
                continue
        
        # Strategy 3: Find JSON-like structure
        brace_pattern = r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}'
        matches = re.findall(brace_pattern, text, re.DOTALL)
        for match in matches:
            try:
                return json.loads(match)
            except json.JSONDecodeError:
                continue
        
        # Strategy 4: Use custom extraction rules if defined
        if 'extraction_rules' in self.config:
            return self._extract_with_rules(text)
        
        return {"raw_text": text, "parse_error": "No valid JSON found"}
    
    def _extract_with_rules(self, text: str) -> Dict[str, Any]:
        """Extract data using custom rules."""
        result = {}
        for field, rule in self.config['extraction_rules'].items():
            if rule['type'] == 'regex':
                match = re.search(rule['pattern'], text, re.IGNORECASE | re.DOTALL)
                if match:
                    value = match.group(1) if match.groups() else match.group(0)
                    if rule.get('transform') == 'int':
                        try:
                            value = int(value)
                        except ValueError:
                            pass
                    elif rule.get('transform') == 'float':
                        try:
                            value = float(value)
                        except ValueError:
                            pass
                    result[field] = value
        return result


class RegexParser(ConfigurableParser):
    """Regex parser with multiple pattern support."""
    
    def parse(self, text: str) -> Dict[str, Any]:
        """Parse text using regex patterns defined in config."""
        result = {}
        
        if 'patterns' not in self.config:
            return {"raw_text": text, "parse_error": "No regex patterns defined"}
        
        for field, pattern_config in self.config['patterns'].items():
            pattern = pattern_config['pattern']
            flags = 0
            
            # Handle regex flags
            if pattern_config.get('ignorecase', True):
                flags |= re.IGNORECASE
            if pattern_config.get('dotall', False):
                flags |= re.DOTALL
            if pattern_config.get('multiline', False):
                flags |= re.MULTILINE
            
            match = re.search(pattern, text, flags)
            if match:
                if match.groups():
                    value = match.group(1)
                else:
                    value = match.group(0)
                
                # Apply transformations
                if 'transform' in pattern_config:
                    value = self._apply_transform(value, pattern_config['transform'])
                
                result[field] = value
        
        return result
    
    def _apply_transform(self, value: str, transform: Dict[str, Any]) -> Any:
        """Apply transformation to the extracted value."""
        if transform.get('type') == 'int':
            try:
                return int(value.strip())
            except ValueError:
                return None
        elif transform.get('type') == 'float':
            try:
                return float(value.strip())
            except ValueError:
                return None
        elif transform.get('type') == 'list':
            delimiter = transform.get('delimiter', ',')
            return [item.strip() for item in value.split(delimiter) if item.strip()]
        elif transform.get('type') == 'strip':
            return value.strip()
        
        return value


class CustomParser(ConfigurableParser):
    """Custom parser that supports Python function definitions."""
    
    def parse(self, text: str) -> Dict[str, Any]:
        """Parse using custom function defined in config."""
        if 'function' not in self.config:
            return {"raw_text": text, "parse_error": "No custom function defined"}
        
        try:
            custom_func = self.config['function']
            if callable(custom_func):
                result = custom_func(text)
                
                # Ensure the result is a dictionary
                if isinstance(result, dict):
                    return result
                else:
                    # Convert non-dict results to dict format
                    return {
                        "raw_text": text,
                        "parsed_result": result,
                        "result_type": type(result).__name__
                    }
            else:
                return {"raw_text": text, "parse_error": "Custom function not callable"}
        except Exception as e:
            return {"raw_text": text, "parse_error": f"Custom parser error: {str(e)}"}




class ParserFactory:
    """Factory class for creating parser instances."""
    
    _parsers = {
        'json': JSONParser,
        'regex': RegexParser,
        'custom': CustomParser
    }
    
    @classmethod
    def create_parser(cls, parser_type: str, config: Dict[str, Any]) -> ConfigurableParser:
        """Create a parser instance based on type and configuration."""
        if parser_type not in cls._parsers:
            raise ValueError(f"Unknown parser type: {parser_type}")
        
        parser_class = cls._parsers[parser_type]
        return parser_class(config)
    
    @classmethod
    def get_available_parsers(cls) -> List[str]:
        """Get list of available parser types."""
        return list(cls._parsers.keys())