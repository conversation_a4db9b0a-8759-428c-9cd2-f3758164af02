"""
Unified prompt management system that reads prompts from database.
Uses prompt_service.py for all database operations and parser.py for result parsing.
"""

import time
import uuid
from typing import Dict, Any, List, Optional

from db.database import get_db_manager
from nlp.llm.parser import ParserFactory
from utils.logging_config import get_nlp_logger

logger = get_nlp_logger(__name__)



class PromptManager:
    """
    Unified prompt manager that reads all prompts from database.
    Handles prompt retrieval, result parsing, and execution tracking.
    """
    
    def __init__(self):
        """Initialize the prompt manager."""
        self.db_manager = None
        self.prompt_service = None
        self._initialize_db_connection()
    
    def _initialize_db_connection(self):
        """Initialize database connection and prompt service."""
        try:
            self.db_manager = get_db_manager()
            self.prompt_service = self.db_manager.prompt_service
        except Exception as e:
            logger.error(f"Failed to initialize database connection: {e}")
            # Will fall back to error responses if database is unavailable
    
    def get_prompt(self, prompt_type: str, version: Optional[int] = None) -> Dict[str, str]:
        """
        Get prompt template and system prompt for a specific analysis type.
        
        Args:
            prompt_type: Type of analysis to perform (e.g., 'market_prediction', 'influence_tagging')
            version: Optional specific version to retrieve
            
        Returns:
            Dict containing prompt_template and system_prompt
        """
        if not self.prompt_service:
            return self._get_error_response("Database connection not available")
        
        try:
            # Get prompt template from database
            template_data = self.prompt_service.get_prompt_template_by_name(prompt_type, version)
            
            if not template_data:
                return self._get_error_response(f"Prompt template '{prompt_type}' not found")
            
            return {
                "prompt_template": template_data['user_template'],
                "system_prompt": template_data['system_prompt']
            }
            
        except Exception as e:
            logger.error(f"Error retrieving prompt '{prompt_type}': {e}")
            return self._get_error_response(f"Error retrieving prompt: {str(e)}")
    
    def get_available_types(self) -> List[str]:
        """Get list of available prompt types from database."""
        if not self.prompt_service:
            return []
        
        try:
            templates = self.prompt_service.list_prompt_templates(active_only=True)
            return [template['name'] for template in templates]
        except Exception as e:
            logger.error(f"Error getting available prompt types: {e}")
            return []
    
    def parse_result(self, 
                    completion: str, 
                    prompt_type: str, 
                    version: Optional[int] = None,
                    track_execution: bool = True) -> Dict[str, Any]:
        """
        Parse and format the completion based on the prompt type.
        
        Args:
            completion: Raw completion text from the model
            prompt_type: Type of analysis performed
            version: Optional specific version used
            track_execution: Whether to track this execution in database
            
        Returns:
            Dict containing parsed and formatted results
        """
        if not self.prompt_service:
            return {"raw_completion": completion, "parse_error": "Database connection not available"}
        
        start_time = time.time()
        
        try:
            # Get prompt template configuration
            template_data = self.prompt_service.get_prompt_template_by_name(prompt_type, version)
            
            if not template_data:
                return {"raw_completion": completion, "parse_error": f"Prompt template '{prompt_type}' not found"}
            
            # Create parser based on template configuration
            parser_type = template_data.get('parser_type', 'json')
            parser_config = template_data.get('parser_config', {})
            
            parser = ParserFactory.create_parser(parser_type, parser_config)
            
            # Parse the completion
            result = parser.parse(completion)
            success = 'parse_error' not in result
            error_message = result.get('parse_error')
            
            # Track execution if requested and template ID is available
            if track_execution and template_data.get('id'):
                execution_time = time.time() - start_time
                self._track_execution(
                    template_data['id'], 
                    completion, 
                    result, 
                    execution_time, 
                    success, 
                    error_message
                )
            
            return result
            
        except Exception as e:
            logger.error(f"Error parsing result for '{prompt_type}': {e}")
            return {"raw_completion": completion, "parse_error": f"Parser error: {str(e)}"}
    
    def _track_execution(self, 
                        template_id: str,
                        raw_response: str,
                        output_data: Dict[str, Any],
                        execution_time: float,
                        success: bool,
                        error_message: Optional[str] = None):
        """Track prompt execution for performance monitoring."""
        try:
            # Note: This would require adding execution tracking to prompt_service
            # For now, we'll log the execution
            logger.info(f"Prompt execution tracked - Template: {template_id}, "
                       f"Success: {success}, Time: {execution_time:.3f}s, "
                       f"Response length: {len(raw_response)}, "
                       f"Output keys: {list(output_data.keys())}, "
                       f"Error: {error_message or 'None'}")
        except Exception as e:
            logger.error(f"Error tracking execution: {e}")
    
    def _get_error_response(self, error_message: str) -> Dict[str, str]:
        """Return standardized error response."""
        return {
            "prompt_template": "",
            "system_prompt": "",
            "error": error_message
        }
    
    def get_prompt_template_by_id(self, template_id: str) -> Optional[Dict[str, Any]]:
        """Get a specific prompt template by ID."""
        if not self.prompt_service:
            return None
        
        try:
            template = self.prompt_service.get_prompt_template(template_id)
            return template
        except Exception as e:
            logger.error(f"Error retrieving template by ID '{template_id}': {e}")
            return None
    
    def create_prompt_template(self, 
                             name: str,
                             system_prompt: str,
                             user_template: str,
                             parser_type: str = 'json',
                             parser_config: Optional[Dict[str, Any]] = None,
                             description: Optional[str] = None,
                             tags: Optional[List[str]] = None,
                             created_by: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """Create a new prompt template."""
        if not self.prompt_service:
            return None
        
        try:
            template_data = {
                'id': str(uuid.uuid4()),
                'name': name,
                'system_prompt': system_prompt,
                'user_template': user_template,
                'parser_type': parser_type,
                'parser_config': parser_config or {},
                'description': description,
                'tags': tags or [],
                'created_by': created_by or 'system'
            }
            
            return self.prompt_service.create_prompt_template(template_data)
        except Exception as e:
            logger.error(f"Error creating prompt template: {e}")
            return None
    
    def update_prompt_template(self, 
                             template_id: str,
                             **updates) -> Optional[Dict[str, Any]]:
        """Update an existing prompt template."""
        if not self.prompt_service:
            return None
        
        try:
            return self.prompt_service.update_prompt_template(template_id, updates)
        except Exception as e:
            logger.error(f"Error updating prompt template: {e}")
            return None
    
    def list_prompt_templates(self, 
                            name: Optional[str] = None,
                            active_only: bool = True,
                            limit: int = 100,
                            offset: int = 0) -> List[Dict[str, Any]]:
        """List prompt templates with optional filtering."""
        if not self.prompt_service:
            return []
        
        try:
            return self.prompt_service.list_prompt_templates(
                name=name,
                active_only=active_only,
                limit=limit,
                offset=offset
            )
        except Exception as e:
            logger.error(f"Error listing prompt templates: {e}")
            return []
        
    def delete_prompt_template(self, template_id: str) -> bool:
        """Delete a prompt template."""
        if not self.prompt_service:
            return False
        
        try:
            return self.prompt_service.delete_prompt_template(template_id)
        except Exception as e:
            logger.error(f"Error deleting prompt template: {e}")
            return False
    
    def get_prompt_short_name(self, prompt_type: str) -> Optional[str]:
        """Get the short name for a prompt type from database."""
        if not self.prompt_service:
            return None
        
        try:
            return self.prompt_service.get_prompt_short_name(prompt_type)
        except Exception as e:
            logger.error(f"Error getting short name for '{prompt_type}': {e}")
            return None


# Global instance for backward compatibility
_prompt_manager = None

def get_prompt_manager() -> PromptManager:
    """Get or create the global prompt manager instance."""
    global _prompt_manager
    if _prompt_manager is None:
        _prompt_manager = PromptManager()
    return _prompt_manager


# Backward compatibility functions
def get_prompt(prompt_type: str, version: Optional[int] = None) -> Dict[str, str]:
    """Get prompt for backward compatibility."""
    return get_prompt_manager().get_prompt(prompt_type, version)


def parse_result(completion: str, prompt_type: str, version: Optional[int] = None) -> Dict[str, Any]:
    """Parse result for backward compatibility."""
    return get_prompt_manager().parse_result(completion, prompt_type, version)


def get_available_types() -> List[str]:
    """Get available types for backward compatibility."""
    return get_prompt_manager().get_available_types()


def get_prompt_short_name(prompt_type: str) -> Optional[str]:
    """Get prompt short name for backward compatibility."""
    return get_prompt_manager().get_prompt_short_name(prompt_type)