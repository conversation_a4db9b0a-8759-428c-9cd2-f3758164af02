from setuptools import setup, find_packages

setup(
    name="newsmonitor",
    version="0.2.0",
    packages=find_packages(),
    include_package_data=True,
    install_requires=[
        # Predictor dependencies
        "torch>=2.0.1",
        "transformers>=4.30.2",
        "datasets==2.14.5",
        "numpy==1.24.3",  # This version is compatible with PyTorch 2.0.1
        "pandas>=1.3.0",
        "scikit-learn>=1.0.0",
        "matplotlib>=3.4.0",
        "tqdm>=4.60.0",
        "accelerate>=0.20.0",
        "sentencepiece>=0.1.99",
        "tensorboard>=2.10.0",
        "yfinance>=0.2.0",

        # News crawler dependencies
        "scrapy>=2.5.0",
        "beautifulsoup4>=4.9.0",
        "python-dateutil>=2.8.0",

        # NLP module dependencies
        "nltk>=3.6.0",
        "spacy>=3.0.0",
        "anthropic",
        "google-genai",

        # Database dependencies
        "chromadb",
        "sentence-transformers",

        # Web interface dependencies
        "flask>=2.0.0",
        "plotly>=5.0.0",
        "requests>=2.25.0",

        # Dashboard
        "streamlit"
    ],
    entry_points={
        'console_scripts': [
            'run-crawler=news_crawler.run_spiders:main',
            'run-web=web.app:main',
            'run-predictor=predictor.run_predictor:main',
        ],
    },
    author="NewsMonitor Team",
    author_email="<EMAIL>",
    description="Financial news monitor with SP500 price predictor",
    keywords="finance, machine learning, nlp, stock prediction, web crawler, financial news",
    python_requires=">=3.8",
)
