from datetime import date, datetime
from decimal import Decimal
from functools import wraps
import time
from sqlalchemy.exc import IntegrityError, OperationalError, SQLAlchemyError

from utils.logging_config import configure_logging

# Configure logging
logger = configure_logging(__name__, log_file='database.log')

DEFAULT_MAX_RETRIES = 3
DEFAULT_RETRY_DELAY = 2


def with_retries(max_retries: int = DEFAULT_MAX_RETRIES, delay: float = DEFAULT_RETRY_DELAY):
    """
    Retry decorator for database operations.

    Args:
        max_retries: Maximum number of retry attempts
        delay: Delay between retries in seconds
    """
    def decorator(fn):
        @wraps(fn)
        def wrapper(*args, **kwargs):
            for attempt in range(1, max_retries + 1):
                try:
                    return fn(*args, **kwargs)
                except (OperationalError, IntegrityError) as e:
                    logger.warning(
                        "Attempt %d/%d failed for %s: %s",
                        attempt, max_retries, fn.__name__, str(e)
                    )
                    if attempt == max_retries:
                        logger.error(
                            "Max retries reached for %s. Raising error.", fn.__name__)
                        raise
                    time.sleep(delay * attempt)  # Exponential backoff
                except Exception as e:
                    logger.error("Non-retryable error in %s: %s",
                                 fn.__name__, str(e))
                    raise
        return wrapper
    return decorator

def to_json_serializable(obj):
    """
    Recursively convert non-JSON-serializable objects to serializable format
    """
    if isinstance(obj, (datetime, date)):
        return obj.isoformat()
    elif isinstance(obj, Decimal):
        return float(obj)
    elif isinstance(obj, dict):
        return {k: to_json_serializable(v) for k, v in obj.items()}
    elif isinstance(obj, (list, tuple)):
        return [to_json_serializable(item) for item in obj]
    elif isinstance(obj, set):
        return list(obj)
    return obj
