"""
Database service for managing LLM fine-tuning jobs.

This service provides CRUD operations for fine-tuning job tracking,
including job creation, status updates, metrics storage, and querying.
"""

from datetime import datetime, timezone
from typing import Any, Dict, List, Optional
from sqlalchemy import and_, func, select, update
from sqlalchemy.dialects.postgresql import insert as pg_insert

from db.database_connection import DatabaseConnection
from db.models import LlmFineTuningJobs
from db.helper import with_retries
from utils.logging_config import configure_logging

# Configure logging
logger = configure_logging(__name__, log_file='database.log')


class FineTuningService:
    """Service for managing LLM fine-tuning jobs in the database."""
    
    def __init__(self, connection: DatabaseConnection):
        self.connection = connection

    @with_retries()
    def create_fine_tuning_job(self, job_data: Dict[str, Any]) -> str:
        """
        Create a new fine-tuning job record.

        Args:
            job_data: Dictionary containing job information

        Returns:
            Job ID of the created record

        Raises:
            SQLAlchemyError if the operation fails
        """
        # Set default values
        job_data.setdefault('created_at', datetime.now(timezone.utc))
        job_data.setdefault('status', 'pending')
        job_data.setdefault('job_type', 'fine_tuning')

        with self.connection.get_session() as session:
            job = LlmFineTuningJobs(**job_data)
            session.add(job)
            session.flush()
            job_id = job.id
            
        logger.info(f"Created fine-tuning job: {job_id}")
        return job_id

    @with_retries()
    def update_fine_tuning_job(self, job_id: str, updates: Dict[str, Any]) -> bool:
        """
        Update an existing fine-tuning job.

        Args:
            job_id: ID of the job to update
            updates: Dictionary of fields to update

        Returns:
            True if job was updated, False if not found

        Raises:
            SQLAlchemyError if the operation fails
        """
        with self.connection.get_session() as session:
            stmt = update(LlmFineTuningJobs).where(
                LlmFineTuningJobs.id == job_id
            ).values(**updates)
            
            result = session.execute(stmt)
            updated = result.rowcount > 0
            
        if updated:
            logger.info(f"Updated fine-tuning job: {job_id}")
        else:
            logger.warning(f"Fine-tuning job not found for update: {job_id}")
            
        return updated

    @with_retries()
    def get_fine_tuning_job(self, job_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a fine-tuning job by ID.

        Args:
            job_id: ID of the job to retrieve

        Returns:
            Job data as dictionary, or None if not found
        """
        with self.connection.get_session() as session:
            stmt = select(LlmFineTuningJobs).where(LlmFineTuningJobs.id == job_id)
            result = session.execute(stmt).first()
            
            if result:
                return result[0].to_dict()
            return None

    @with_retries()
    def get_fine_tuning_jobs(
        self,
        api: Optional[str] = None,
        status: Optional[List[str]] = None,
        model_name: Optional[str] = None,
        limit: Optional[int] = None,
        order_by: str = 'created_at',
        order_desc: bool = True
    ) -> List[Dict[str, Any]]:
        """
        Get fine-tuning jobs with optional filtering.

        Args:
            api: Filter by API name
            status: Filter by status list
            model_name: Filter by model name
            limit: Maximum number of results
            order_by: Field to order by
            order_desc: Whether to order in descending order

        Returns:
            List of job data dictionaries
        """
        with self.connection.get_session() as session:
            stmt = select(LlmFineTuningJobs)
            
            # Apply filters
            if api:
                stmt = stmt.where(LlmFineTuningJobs.api == api)
            if status:
                stmt = stmt.where(LlmFineTuningJobs.status.in_(status))
            if model_name:
                stmt = stmt.where(LlmFineTuningJobs.model_name == model_name)
            
            # Apply ordering
            order_field = getattr(LlmFineTuningJobs, order_by, LlmFineTuningJobs.created_at)
            if order_desc:
                stmt = stmt.order_by(order_field.desc())
            else:
                stmt = stmt.order_by(order_field)
            
            # Apply limit
            if limit:
                stmt = stmt.limit(limit)
            
            results = session.execute(stmt).all()
            return [job[0].to_dict() for job in results]

    @with_retries()
    def get_active_fine_tuning_jobs(self, api: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get all active (pending or running) fine-tuning jobs.

        Args:
            api: Optional API filter

        Returns:
            List of active job data dictionaries
        """
        return self.get_fine_tuning_jobs(
            api=api,
            status=['pending', 'running'],
            order_by='created_at',
            order_desc=False
        )

    @with_retries()
    def get_job_statistics(self, api: Optional[str] = None) -> Dict[str, Any]:
        """
        Get statistics about fine-tuning jobs.

        Args:
            api: Optional API filter

        Returns:
            Dictionary with job statistics
        """
        with self.connection.get_session() as session:
            stmt = select(
                LlmFineTuningJobs.status,
                func.count(LlmFineTuningJobs.id).label('count'),
                func.sum(LlmFineTuningJobs.cost).label('total_cost'),
                func.sum(LlmFineTuningJobs.training_tokens).label('total_tokens')
            )
            
            if api:
                stmt = stmt.where(LlmFineTuningJobs.api == api)
            
            stmt = stmt.group_by(LlmFineTuningJobs.status)
            
            results = session.execute(stmt).all()
            
            stats = {
                'by_status': {},
                'total_jobs': 0,
                'total_cost': 0.0,
                'total_tokens': 0
            }
            
            for row in results:
                status, count, cost, tokens = row
                stats['by_status'][status] = {
                    'count': count,
                    'cost': cost or 0.0,
                    'tokens': tokens or 0
                }
                stats['total_jobs'] += count
                stats['total_cost'] += cost or 0.0
                stats['total_tokens'] += tokens or 0
            
            return stats

    @with_retries()
    def mark_job_started(self, job_id: str) -> bool:
        """
        Mark a job as started with current timestamp.

        Args:
            job_id: ID of the job to mark as started

        Returns:
            True if successful, False if job not found
        """
        return self.update_fine_tuning_job(job_id, {
            'status': 'running',
            'started_at': datetime.now(timezone.utc)
        })

    @with_retries()
    def mark_job_completed(self, job_id: str, final_metrics: Optional[Dict[str, Any]] = None,
                          fine_tuned_model_name: Optional[str] = None) -> bool:
        """
        Mark a job as completed with final metrics.

        Args:
            job_id: ID of the job to mark as completed
            final_metrics: Final training metrics
            fine_tuned_model_name: Name of the fine-tuned model

        Returns:
            True if successful, False if job not found
        """
        updates = {
            'status': 'completed',
            'completed_at': datetime.now(timezone.utc)
        }
        
        if final_metrics:
            updates['final_metrics'] = final_metrics
        if fine_tuned_model_name:
            updates['fine_tuned_model_name'] = fine_tuned_model_name
            
        return self.update_fine_tuning_job(job_id, updates)

    @with_retries()
    def mark_job_failed(self, job_id: str, error_message: str) -> bool:
        """
        Mark a job as failed with error message.

        Args:
            job_id: ID of the job to mark as failed
            error_message: Error message describing the failure

        Returns:
            True if successful, False if job not found
        """
        return self.update_fine_tuning_job(job_id, {
            'status': 'failed',
            'completed_at': datetime.now(timezone.utc),
            'error_message': error_message
        })
