
from datetime import date
from typing import Any, Dict, List, Optional, Tuple, Union

import pandas as pd
from sqlalchemy import Date, cast, select
from sqlalchemy.dialects.postgresql import insert as pg_insert

from db.database_connection import DatabaseConnection
from db.helper import with_retries
from db.models import MarketData

from utils.logging_config import configure_logging

# Configure logging
logger = configure_logging(__name__, log_file='database.log')


class MarketDataService:
    def __init__(self, connection: DatabaseConnection):
        self.connection = connection

    @with_retries()
    def upsert_market_data(self, ticker: str, interval: str, data: pd.DataFrame) -> int:
        """
        Insert or update market data using PostgreSQL UPSERT (no created_at or updated_at).

        Args:
            ticker: Stock ticker symbol
            interval: Data interval (e.g., '1d', '1h')
            data: DataFrame with market data (index should be datetime)

        Returns:
            Number of records inserted or updated
        """
        if data.empty:
            return 0

        with self.connection.get_session() as session:
            records = []

            for index, row in data.iterrows():
                records.append({
                    "ticker": ticker,
                    "date": index,
                    "interval": interval,
                    "open_price": row.get('Open'),
                    "high_price": row.get('High'),
                    "low_price": row.get('Low'),
                    "close_price": row.get('Close'),
                    "volume": row.get('Volume')
                })

            stmt = pg_insert(MarketData).values(records)

            stmt = stmt.on_conflict_do_update(
                index_elements=["ticker", "date", "interval"],
                set_={
                    "open_price": stmt.excluded.open_price,
                    "high_price": stmt.excluded.high_price,
                    "low_price": stmt.excluded.low_price,
                    "close_price": stmt.excluded.close_price,
                    "volume": stmt.excluded.volume
                }
            )

            session.execute(stmt)
            logger.info(
                f"Upserted {len(records)} market data records for {ticker} ({interval})")
            return len(records)

    @with_retries()
    def get_market_data(
        self,
        ticker: str,
        interval: str = '1d',
        start_date: Optional[date] = None,
        end_date: Optional[date] = None
    ) -> pd.DataFrame:
        """
        Get market data from the PostgreSQL database.

        Args:
            ticker: Stock ticker symbol
            interval: Data interval
            start_date: Start date (YYYY-MM-DD)
            end_date: End date (YYYY-MM-DD)

        Returns:
            DataFrame with market data
        """
        with self.connection.get_session() as session:
            stmt = select(
                MarketData.date,
                MarketData.open_price,
                MarketData.high_price,
                MarketData.low_price,
                MarketData.close_price,
                MarketData.volume
            ).where(
                MarketData.ticker == ticker,
                MarketData.interval == interval
            )

            if start_date:
                stmt = stmt.where(cast(MarketData.date, Date) >= start_date)
            if end_date:
                stmt = stmt.where(cast(MarketData.date, Date) <= end_date)

            stmt = stmt.order_by(MarketData.date)

            results = session.execute(stmt).all()

            if not results:
                return pd.DataFrame()

            df = pd.DataFrame(results, columns=[
                              'Datetime', 'Open', 'High', 'Low', 'Close', 'Volume'])
            df.set_index('Datetime', inplace=True)
            return df
