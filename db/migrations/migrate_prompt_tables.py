#!/usr/bin/env python3
"""
Database migration script to create prompt management tables.
Run this script to add the new prompt template and execution tracking tables.
"""

import argparse
import sys
import os

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from db.database import get_db_manager
from db.models import PromptTemplate, PromptExecution, Base
from utils.logging_config import configure_logging

logger = configure_logging(__name__)


def main():
    """Run the migration to create prompt management tables."""
    print("Starting prompt management tables migration...")

    parser = argparse.ArgumentParser(description='API Provider Initialization Script')
    parser.add_argument('--database-url', type=str, help='Database URL')
    args = parser.parse_args()

    
    try:
        db_manager = get_db_manager(args.database_url)
        engine = db_manager.connection.engine
        
        # Create the new tables
        print("Creating PromptTemplate and PromptExecution tables...")
        Base.metadata.create_all(bind=engine, tables=[
            PromptTemplate.__table__,
            PromptExecution.__table__
        ])
        
        print("✅ Prompt management tables created successfully!")
        
        # Optionally, run the migration of existing prompts
        print("\nMigrating existing prompts...")
        try:
            from nlp.llm.prompt import get_prompt_manager
            
            # Use the new unified prompt manager to create any needed templates
            prompt_manager = get_prompt_manager()
            
            # Create default market_prediction template if it doesn't exist
            if not prompt_manager.prompt_service.get_prompt_template_by_name('market_prediction'):
                # This would need to be populated with actual prompt content
                print("Default prompts should be created manually via the admin interface")
            
            print("✅ Prompt manager initialized successfully!")
            
        except Exception as e:
            print(f"⚠️  Prompt manager initialization failed (this is okay): {e}")
        
        print("\n🎉 Migration completed successfully!")
        print("\nYou can now:")
        print("1. Access the admin web interface to manage prompts")
        print("2. Use the enhanced prompt manager with database backend")
        print("3. Create versioned prompts with configurable parsers")
        
    except Exception as e:
        logger.error(f"Migration failed: {e}")
        print(f"❌ Migration failed: {e}")
        sys.exit(1)
    
    finally:
        if 'db_manager' in locals():
            db_manager.close()


if __name__ == "__main__":
    main()