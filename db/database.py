"""
Database module for managing articles and chunks with vector similarity search.

This module provides a comprehensive database interface for storing and querying
articles with their associated text chunks, including vector embeddings for
semantic search capabilities.
"""

import os

from dotenv import load_dotenv
from sqlalchemy import text

from db.api_info_service import ApiInfoService
from db.database_connection import DatabaseConnection
from db.article_service import ArticleService
from db.market_data_service import MarketDataService
from db.llm_api_service import LlmApiService
from db.fine_tuning_service import FineTuningService
from db.models import Base
from cloud.cloud_config import get_cloud_config

from db.prompt_service import PromptService
from utils.logging_config import configure_logging

# Configure logging
logger = configure_logging(__name__, log_file='database.log')


class DatabaseInitializer:
    """Handles database schema and index creation."""

    def __init__(self, connection: DatabaseConnection):
        self.connection = connection

    def initialize(self):
        """Initialize database schema and indexes."""
        self._create_extensions()
        self._create_schema()

    def _create_extensions(self):
        """Create required PostgreSQL extensions."""
        with self.connection.engine.begin() as conn:
            logger.info("Installing pgvector extension...")
            conn.execute(text("CREATE EXTENSION IF NOT EXISTS vector"))

    def _create_schema(self):
        """Create database tables."""
        logger.info("Creating database schema...")
        Base.metadata.create_all(bind=self.connection.engine)


class DatabaseManager:
    """
    Main database manager class for article and chunk operations.

    Provides high-level interface for storing, querying, and managing
    articles and their associated text chunks with vector embeddings.
    """

    def __init__(self, database_url: str = None):
        database_url = database_url or os.getenv("DATABASE_URL", "postgresql://localhost/articles_db")
        self.connection = DatabaseConnection(database_url)

        # Initialize database
        initializer = DatabaseInitializer(self.connection)
        initializer.initialize()

        self.article_service = ArticleService(self.connection)
        self.market_data_service = MarketDataService(self.connection)
        self.llm_api_service = LlmApiService(self.connection)
        self.fine_tuning_service = FineTuningService(self.connection)
        self.prompt_service = PromptService(self.connection)
        self.api_info_service = ApiInfoService(self.connection)

        logger.info("DatabaseManager initialized successfully")

    def close(self):
        """Close database connections and cleanup resources."""
        logger.info("Closing DatabaseManager...")
        self.connection.close()


_db_manager = None


def get_db_manager(database_url: str = None):
    global _db_manager
    if _db_manager is None:
        _db_manager = DatabaseManager(database_url)
    return _db_manager