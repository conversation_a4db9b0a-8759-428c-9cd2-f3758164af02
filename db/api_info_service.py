"""
Service for managing API provider and model configurations.
"""

from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from db.database_connection import DatabaseConnection
from db.models import ApiInfo
from utils.logging_config import get_db_logger

logger = get_db_logger(__name__)


class ApiInfoService:
    """Service for managing API provider and model configurations."""

    def __init__(self, connection: DatabaseConnection):
        """Initialize the service with database connection."""
        self.connection = connection

    def get_all_api_info(self, active_only: bool = False) -> List[Dict[str, Any]]:
        """
        Get all API provider and model configurations.
        
        Args:
            active_only: If True, only return active configurations
            
        Returns:
            List of API info dictionaries
        """
        try:
            with self.connection.get_session() as session:
                query = session.query(ApiInfo)
                
                if active_only:
                    query = query.filter(ApiInfo.is_active == True)
                
                api_infos = query.order_by(
                    ApiInfo.provider_name, 
                    ApiInfo.model_name
                ).all()
                
                return [api_info.to_dict() for api_info in api_infos]
                
        except Exception as e:
            logger.error(f"Error getting API info: {e}")
            return []

    def get_api_info_by_id(self, api_info_id: int) -> Optional[Dict[str, Any]]:
        """
        Get API info by ID.
        
        Args:
            api_info_id: The API info ID
            
        Returns:
            API info dictionary or None if not found
        """
        try:
            with self.connection.get_session() as session:
                api_info = session.query(ApiInfo).filter(
                    ApiInfo.id == api_info_id
                ).first()
                
                return api_info.to_dict() if api_info else None
                
        except Exception as e:
            logger.error(f"Error getting API info by ID {api_info_id}: {e}")
            return None

    def get_default_api_info(self) -> Optional[Dict[str, Any]]:
        """
        Get the default API provider and model configuration.
        
        Returns:
            Default API info dictionary or None if not found
        """
        try:
            with self.connection.get_session() as session:
                api_info = session.query(ApiInfo).filter(
                    and_(ApiInfo.is_default == True, ApiInfo.is_active == True)
                ).first()
                
                return api_info.to_dict() if api_info else None
                
        except Exception as e:
            logger.error(f"Error getting default API info: {e}")
            return None

    def get_api_info_by_provider_model(
        self, 
        provider_name: str, 
        model_name: str
    ) -> Optional[Dict[str, Any]]:
        """
        Get API info by provider and model name.
        
        Args:
            provider_name: The API provider name
            model_name: The model name
            
        Returns:
            API info dictionary or None if not found
        """
        try:
            with self.connection.get_session() as session:
                api_info = session.query(ApiInfo).filter(
                    and_(
                        ApiInfo.provider_name == provider_name,
                        ApiInfo.model_name == model_name
                    )
                ).first()
                
                return api_info.to_dict() if api_info else None
                
        except Exception as e:
            logger.error(f"Error getting API info for {provider_name}/{model_name}: {e}")
            return None

    def create_api_info(self, api_info_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Create a new API info configuration.
        
        Args:
            api_info_data: Dictionary containing API info data
            
        Returns:
            Created API info dictionary or None if failed
        """
        try:
            with self.connection.get_session() as session:
                # Check if this provider/model combination already exists
                existing = session.query(ApiInfo).filter(
                    and_(
                        ApiInfo.provider_name == api_info_data['provider_name'],
                        ApiInfo.model_name == api_info_data['model_name']
                    )
                ).first()
                
                if existing:
                    logger.warning(
                        f"API info for {api_info_data['provider_name']}/{api_info_data['model_name']} already exists"
                    )
                    return None
                
                # Create new API info
                api_info = ApiInfo(
                    provider_name=api_info_data['provider_name'],
                    model_name=api_info_data['model_name'],
                    display_name=api_info_data.get('display_name'),
                    is_active=api_info_data.get('is_active', True),
                    is_default=api_info_data.get('is_default', False),
                    configuration=api_info_data.get('configuration', {}),
                    description=api_info_data.get('description')
                )
                
                session.add(api_info)
                session.commit()
                session.refresh(api_info)
                
                logger.info(f"Created API info for {api_info.provider_name}/{api_info.model_name}")
                return api_info.to_dict()
                
        except Exception as e:
            logger.error(f"Error creating API info: {e}")
            return None

    def update_api_info(
        self, 
        api_info_id: int, 
        update_data: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """
        Update an existing API info configuration.
        
        Args:
            api_info_id: The API info ID to update
            update_data: Dictionary containing fields to update
            
        Returns:
            Updated API info dictionary or None if failed
        """
        try:
            with self.connection.get_session() as session:
                api_info = session.query(ApiInfo).filter(
                    ApiInfo.id == api_info_id
                ).first()
                
                if not api_info:
                    logger.warning(f"API info with ID {api_info_id} not found")
                    return None
                
                # Update fields
                for field, value in update_data.items():
                    if hasattr(api_info, field):
                        setattr(api_info, field, value)
                
                session.commit()
                session.refresh(api_info)
                
                logger.info(f"Updated API info {api_info_id}")
                return api_info.to_dict()
                
        except Exception as e:
            logger.error(f"Error updating API info {api_info_id}: {e}")
            return None

    def delete_api_info(self, api_info_id: int) -> bool:
        """
        Delete an API info configuration.
        
        Args:
            api_info_id: The API info ID to delete
            
        Returns:
            True if deleted successfully, False otherwise
        """
        try:
            with self.connection.get_session() as session:
                api_info = session.query(ApiInfo).filter(
                    ApiInfo.id == api_info_id
                ).first()
                
                if not api_info:
                    logger.warning(f"API info with ID {api_info_id} not found")
                    return False
                
                # Don't allow deletion of default API info
                if api_info.is_default:
                    logger.warning(f"Cannot delete default API info {api_info_id}")
                    return False
                
                session.delete(api_info)
                session.commit()
                
                logger.info(f"Deleted API info {api_info_id}")
                return True
                
        except Exception as e:
            logger.error(f"Error deleting API info {api_info_id}: {e}")
            return False

    def set_default_api_info(self, api_info_id: int) -> bool:
        """
        Set an API info configuration as the default.
        
        Args:
            api_info_id: The API info ID to set as default
            
        Returns:
            True if set successfully, False otherwise
        """
        try:
            with self.connection.get_session() as session:
                # First, unset all current defaults
                session.query(ApiInfo).update(
                    {ApiInfo.is_default: False}
                )
                
                # Set the new default
                api_info = session.query(ApiInfo).filter(
                    ApiInfo.id == api_info_id
                ).first()
                
                if not api_info:
                    logger.warning(f"API info with ID {api_info_id} not found")
                    return False
                
                api_info.is_default = True
                api_info.is_active = True  # Ensure default is active
                
                session.commit()
                
                logger.info(f"Set API info {api_info_id} as default")
                return True
                
        except Exception as e:
            logger.error(f"Error setting default API info {api_info_id}: {e}")
            return False

    def get_providers_and_models(self) -> Dict[str, List[str]]:
        """
        Get a mapping of providers to their available models.
        
        Returns:
            Dictionary mapping provider names to lists of model names
        """
        try:
            with self.connection.get_session() as session:
                api_infos = session.query(ApiInfo).filter(
                    ApiInfo.is_active == True
                ).order_by(ApiInfo.provider_name, ApiInfo.model_name).all()
                
                providers_models = {}
                for api_info in api_infos:
                    if api_info.provider_name not in providers_models:
                        providers_models[api_info.provider_name] = []
                    providers_models[api_info.provider_name].append(api_info.model_name)
                
                return providers_models
                
        except Exception as e:
            logger.error(f"Error getting providers and models: {e}")
            return {}
