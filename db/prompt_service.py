from typing import Dict, Any, List, Optional
import uuid
from sqlalchemy import desc, and_, func
from datetime import datetime, timedelta

from db.database_connection import DatabaseConnection
from db.models import PromptTemplate, PromptExecution


class PromptService:
    """Service class for prompt template management operations."""
    
    def __init__(self, connection: DatabaseConnection):
        self.connection = connection
    
    def create_prompt_template(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new prompt template."""
        # Get the next version number for this prompt name
        with self.connection.get_session() as session:
            latest = session.query(PromptTemplate)\
                        .filter(PromptTemplate.name == data['name'])\
                        .order_by(desc(PromptTemplate.version))\
                        .first()
            
            id = data.get('id') or str(uuid.uuid4())
            next_version = (latest.version + 1) if latest else 1
            template = PromptTemplate(
                id=id,
                name=data['name'],
                short_name=data.get('short_name'),
                version=next_version,
                is_prod=data.get('is_prod', False),
                system_prompt=data['system_prompt'],
                user_template=data['user_template'],
                parser_type=data.get('parser_type', 'json'),
                parser_config=data.get('parser_config', {}),
                description=data.get('description'),
                tags=data.get('tags', []),
                created_by=data.get('created_by')
            )
            
            session.add(template)
            session.commit()
            
            return template.to_dict()
    
    def get_prompt_template(self, template_id: str) -> Optional[Dict[str, Any]]:
        """Get a prompt template by ID."""
        with self.connection.get_session() as session:
            return session.query(PromptTemplate)\
                        .filter(PromptTemplate.id == template_id)\
                        .first().to_dict()
    
    def get_prompt_template_by_name(self, name: str, version: Optional[int] = None) -> Optional[Dict[str, Any]]:
        """Get a prompt template by name and optionally version."""
        with self.connection.get_session() as session:
            query = session.query(PromptTemplate).filter(PromptTemplate.name == name)
        
            if version is not None:
                query = query.filter(PromptTemplate.version == version)
            else:
                # Get the production version first, fallback to active
                prod_result = query.filter(PromptTemplate.is_prod == True).first()
                if prod_result:
                    return prod_result.to_dict()
                
                # If no prod version, get the active version
                query = query.filter(PromptTemplate.is_active == True)
        
            result = query.order_by(desc(PromptTemplate.version)).first()
            return result.to_dict() if result else None
    
    def update_prompt_template(self, template_id: str, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Update an existing prompt template."""
        with self.connection.get_session() as session:
            template = session.query(PromptTemplate)\
                        .filter(PromptTemplate.id == template_id)\
                        .first()
            
            if not template:
                return None
            
            # Update fields if provided
            for field in ['short_name', 'system_prompt', 'user_template', 'parser_type', 'parser_config', 
                        'description', 'tags', 'is_active', 'is_prod']:
                if field in data:
                    setattr(template, field, data[field])
                        
            session.add(template)
            session.commit()
            return template.to_dict()
    
    def delete_prompt_template(self, template_id: str) -> bool:
        """Delete a prompt template."""
        with self.connection.get_session() as session:
            template = session.query(PromptTemplate)\
                        .filter(PromptTemplate.id == template_id)\
                        .first()
            
            if template:
                session.delete(template)
                session.commit()
                return True
            
            return False
    
    def list_prompt_templates(self, 
                            name: Optional[str] = None,
                            active_only: bool = True,
                            limit: int = 100,
                            offset: int = 0) -> List[Dict[str, Any]]:
        """List prompt templates with optional filtering."""
        with self.connection.get_session() as session:
            query = session.query(PromptTemplate)
            
            if name:
                query = query.filter(PromptTemplate.name.ilike(f'%{name}%'))
            
            if active_only:
                query = query.filter(PromptTemplate.is_active == True)
            
            results = query.order_by(desc(PromptTemplate.created_at))\
                        .offset(offset)\
                        .limit(limit)\
                        .all()
    
            return [r.to_dict() for r in results]

    def get_prompt_versions(self, name: str) -> List[Dict[str, Any]]:
        """Get all versions of a prompt template."""
        with self.connection.get_session() as session:
            results = session.query(PromptTemplate)\
                        .filter(PromptTemplate.name == name)\
                        .order_by(desc(PromptTemplate.version))\
                        .all()

            return [r.to_dict() for r in results]
    
    def activate_prompt_version(self, template_id: str) -> bool:
        """Set a specific version as active and deactivate others with the same name."""
        with self.connection.get_session() as session:
            template = session.query(PromptTemplate)\
                        .filter(PromptTemplate.id == template_id)\
                        .first()
            
            if not template:
                return False
            
            # Deactivate all versions of this prompt
            session.query(PromptTemplate)\
                .filter(PromptTemplate.name == template.name)\
                .update({'is_active': False})
            
            # Activate the specified version
            session.query(PromptTemplate)\
                .filter(PromptTemplate.id == template_id)\
                .update({'is_active': True})
            
            session.commit()
            return True
    
    def get_prompt_statistics(self, template_id: str) -> Dict[str, Any]:
        """Get usage statistics for a prompt template."""
        with self.connection.get_session() as session:
            template = session.query(PromptTemplate)\
                        .filter(PromptTemplate.id == template_id)\
                        .first()
        
            if not template:
                return {}
        
            # Get execution statistics
            total_executions = session.query(PromptExecution)\
                                .filter(PromptExecution.prompt_template_id == template_id)\
                                .count()
        
            successful_executions = session.query(PromptExecution)\
                                        .filter(and_(
                                            PromptExecution.prompt_template_id == template_id,
                                            PromptExecution.success == True
                                        )).count()
            
            # Get recent executions (last 30 days)
            thirty_days_ago = datetime.utcnow() - timedelta(days=30)
            recent_executions = session.query(PromptExecution)\
                                    .filter(and_(
                                        PromptExecution.prompt_template_id == template_id,
                                        PromptExecution.created_at >= thirty_days_ago
                                    )).count()
            
            # Get average execution time
            avg_time_result = session.query(func.avg(PromptExecution.execution_time))\
                                    .filter(and_(
                                        PromptExecution.prompt_template_id == template_id,
                                        PromptExecution.execution_time.isnot(None)
                                    )).scalar()
            
            avg_execution_time = float(avg_time_result) if avg_time_result else 0.0
            
            # Get most common errors
            common_errors = session.query(
                                PromptExecution.error_message,
                                func.count(PromptExecution.error_message).label('count')
                            )\
                            .filter(and_(
                                PromptExecution.prompt_template_id == template_id,
                                PromptExecution.success == False,
                                PromptExecution.error_message.isnot(None)
                            ))\
                            .group_by(PromptExecution.error_message)\
                            .order_by(desc('count'))\
                            .limit(5)\
                            .all()
            
            return {
                'template_id': template_id,
                'name': template.name,
                'version': template.version,
                'total_executions': total_executions,
                'successful_executions': successful_executions,
                'success_rate': (successful_executions / total_executions * 100) if total_executions > 0 else 0,
                'recent_executions_30d': recent_executions,
                'avg_execution_time': avg_execution_time,
                'common_errors': [{'error': error, 'count': count} for error, count in common_errors]
            }
    
    def get_prompt_execution_history(self, 
                                   template_id: str, 
                                   limit: int = 50,
                                   offset: int = 0) -> List[Dict[str, Any]]:
        """Get execution history for a prompt template."""
        with self.connection.get_session() as session:
            results = session.query(PromptExecution)\
                        .filter(PromptExecution.prompt_template_id == template_id)\
                        .order_by(desc(PromptExecution.created_at))\
                        .offset(offset)\
                        .limit(limit)\
                        .all()

            return [r.to_dict() for r in results]
    
    def search_prompt_templates(self, 
                              query: str,
                              active_only: bool = True,
                              limit: int = 50) -> List[Dict[str, Any]]:
        """Search prompt templates by name, description, or tags."""
        with self.connection.get_session() as session:
            search_filter = session.query(PromptTemplate)
        
            # Search in name, description, and tags
            search_conditions = [
                PromptTemplate.name.ilike(f'%{query}%'),
                PromptTemplate.description.ilike(f'%{query}%'),
            ]
        
            # Search in tags array
            search_conditions.append(
                func.array_to_string(PromptTemplate.tags, '|').ilike(f'%{query}%')
            )
        
            search_filter = search_filter.filter(
                func.or_(*search_conditions)
            )
        
            if active_only:
                search_filter = search_filter.filter(PromptTemplate.is_active == True)
        
            results = search_filter.order_by(desc(PromptTemplate.created_at))\
                                .limit(limit)\
                                .all()

            return [r.to_dict() for r in results]
    
    def clone_prompt_template(self, template_id: str, new_name: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """Clone an existing prompt template."""
        with self.connection.get_session() as session:
            original = session.query(PromptTemplate)\
                        .filter(PromptTemplate.id == template_id)\
                        .first()
        
            if not original:
                return None
            
            clone_name = new_name or f"{original.name}_copy"
            
            # Create new template with same content but new name/version
            clone_data = {
                'name': clone_name,
                'system_prompt': original.system_prompt,
                'user_template': original.user_template,
                'parser_type': original.parser_type,
                'parser_config': original.parser_config,
                'description': f"Clone of {original.name} v{original.version}",
                'tags': (original.tags or []) + ['cloned'],
                'created_by': 'system_clone'
            }
            
            return self.create_prompt_template(clone_data)
    
    def get_prompt_template_count(self, active_only: bool = True) -> int:
        """Get total count of prompt templates."""
        with self.connection.get_session() as session:
            query = session.query(PromptTemplate)
            
            if active_only:
                query = query.filter(PromptTemplate.is_active == True)
            
            return query.count()
    
    def export_prompt_template(self, template_id: str) -> Optional[Dict[str, Any]]:
        """Export a prompt template to a dictionary format."""
        with self.connection.get_session() as session:
            template = session.query(PromptTemplate)\
                        .filter(PromptTemplate.id == template_id)\
                        .first()
        
            if not template:
                return None
            
            return {
                'name': template.name,
                'short_name': template.short_name,
                'version': template.version,
                'is_prod': template.is_prod,
                'system_prompt': template.system_prompt,
                'user_template': template.user_template,
                'parser_type': template.parser_type,
                'parser_config': template.parser_config,
                'description': template.description,
                'tags': template.tags,
                'created_at': template.created_at.isoformat(),
                'export_timestamp': datetime.utcnow().isoformat()
            }
    
    def import_prompt_template(self, data: Dict[str, Any], created_by: str = 'import') -> Dict[str, Any]:
        """Import a prompt template from dictionary format."""
        import_data = {
            'name': data['name'],
            'short_name': data.get('short_name'),
            'is_prod': data.get('is_prod', False),
            'system_prompt': data['system_prompt'],
            'user_template': data['user_template'],
            'parser_type': data.get('parser_type', 'json'),
            'parser_config': data.get('parser_config', {}),
            'description': data.get('description', ''),
            'tags': data.get('tags', []) + ['imported'],
            'created_by': created_by
        }
        
        return self.create_prompt_template(import_data)
    
    def get_prompt_short_name(self, name: str) -> Optional[str]:
        """Get the short name for a prompt by name."""
        with self.connection.get_session() as session:
            # Get the production version first, fallback to active
            query = session.query(PromptTemplate).filter(PromptTemplate.name == name)
            
            prod_result = query.filter(PromptTemplate.is_prod == True).first()
            if prod_result:
                return prod_result.short_name
            
            # If no prod version, get the active version
            active_result = query.filter(PromptTemplate.is_active == True).order_by(desc(PromptTemplate.version)).first()
            return active_result.short_name if active_result else None
    
    def set_prompt_prod(self, template_id: str) -> bool:
        """Set a specific prompt template as production and unset others with the same name."""
        with self.connection.get_session() as session:
            template = session.query(PromptTemplate)\
                        .filter(PromptTemplate.id == template_id)\
                        .first()
            
            if not template:
                return False
            
            # Unset production flag for all prompts with the same name
            session.query(PromptTemplate)\
                .filter(PromptTemplate.name == template.name)\
                .update({'is_prod': False})
            
            # Set this template as production
            session.query(PromptTemplate)\
                .filter(PromptTemplate.id == template_id)\
                .update({'is_prod': True})
            
            session.commit()
            return True