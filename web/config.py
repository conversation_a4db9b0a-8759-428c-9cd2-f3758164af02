"""
Configuration settings for the web interface.

This module provides configuration settings for the web interface,
including paths, URLs, and other settings.
"""

import os
from pathlib import Path
from zoneinfo import ZoneInfo

# Project paths
ROOT_DIR = Path(__file__).parent.resolve()
TEMPLATES_DIR = ROOT_DIR / "templates"
STATIC_DIR = ROOT_DIR / "static"
LOG_DIR = ROOT_DIR / "logs"

# Ensure directories exist
os.makedirs(TEMPLATES_DIR, exist_ok=True)
os.makedirs(STATIC_DIR, exist_ok=True)
os.makedirs(LOG_DIR, exist_ok=True)

# Web server settings
HOST = "0.0.0.0"
PORT = 5000
DEBUG = False

# Data settings
DEFAULT_LOOKBACK_DAYS = 30  # Default number of days to look back for data
DEFAULT_ARTICLE_LIMIT = 20  # Default number of articles to return

# Date format
DATE_FORMAT = "%Y-%m-%d"  # YYYY-MM-DD
DATE_TIME_FORMAT = "%Y-%m-%d %H:%M"  # YYYY-MM-DD HH:MM
DISPLAY_DATE_FORMAT = "%b %d, %Y"  # e.g., Jan 01, 2023
TZ = ZoneInfo("America/New_York")

class Config:
    """Configuration class for web interface settings."""
    # Application settings
    BASE_URL = os.environ.get('BASE_URL', 'http://localhost:5000')
    SERVER_NAME = os.environ.get('SERVER_NAME', 'localhost')
    PREFERRED_URL_SCHEME = os.environ.get('PREFERRED_URL_SCHEME', 'http')
    SECRET_KEY = os.getenv("SECRET_KEY", "dev-secret-key")
    SECURITY_PASSWORD_SALT = os.getenv("SECURITY_PASSWORD_SALT", "dev-salt")
    WTF_CSRF_ENABLED = True
    WTF_CSRF_TIME_LIMIT = None  # No time limit for CSRF tokens

    # Database settings
    DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://localhost/articles_db")
    SQLALCHEMY_DATABASE_URI = os.getenv("DATABASE_URL", "postgresql://localhost/articles_db")
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_size': 10,
        'max_overflow': 20,
        'pool_pre_ping': True,
        'echo': False  # Set to True for SQL debugging
    }

    # Email settings
    MAIL_USERNAME = os.getenv("MAIL_USERNAME", "")
    MAIL_PASSWORD = os.getenv("MAIL_PASSWORD", "")
    MAIL_DEFAULT_SENDER = os.getenv("MAIL_DEFAULT_SENDER", "<EMAIL>")
    MAIL_PORT = int(os.environ.get('MAIL_PORT', 587))
    MAIL_SERVER = os.environ.get('MAIL_SERVER', 'smtp.gmail.com')
    MAIL_USE_TLS = True
    MAIL_USE_SSL = False