{% extends "layout.html" %}

{% block title %}Financial News - Market Analysis{% endblock %}

{% block content %}
<!-- Flash Messages -->
{% with messages = get_flashed_messages(with_categories=true) %}
{% if messages %}
<div class="container mt-3">
    {% for category, message in messages %}
    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show"
        role="alert">
        {{ message }}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    {% endfor %}
</div>
{% endif %}
{% endwith %}

<!-- Market Overview Strip -->
<div class="market-overview">
    <div class="market-ticker" id="market-ticker">
        <div class="ticker-item">
            <span class="ticker-label">S&P 500</span>
            <span class="ticker-value" id="sp500-price">Loading...</span>
            <span class="ticker-change" id="sp500-change">--</span>
        </div>
        <div class="ticker-item">
            <span class="ticker-label">Dow</span>
            <span class="ticker-value" id="dow-price">Loading...</span>
            <span class="ticker-change" id="dow-change">--</span>
        </div>
        <div class="ticker-item">
            <span class="ticker-label">Nasdaq</span>
            <span class="ticker-value" id="nasdaq-price">Loading...</span>
            <span class="ticker-change" id="nasdaq-change">--</span>
        </div>
        <div class="ticker-item">
            <span class="ticker-label">SPY</span>
            <span class="ticker-value" id="spy-price">Loading...</span>
            <span class="ticker-change" id="spy-change">--</span>
        </div>
        <div class="ticker-item">
            <span class="ticker-label">VOO</span>
            <span class="ticker-value" id="voo-price">Loading...</span>
            <span class="ticker-change" id="voo-change">--</span>
        </div>
    </div>
</div>

<!-- Search Section -->
<div class="main-content-container">
    <div class="search-section">
        <div class="search-header">
            <h5><i class="bi bi-search"></i> Search News</h5>
            <button class="toggle-advanced" id="toggle-advanced-search">
                <i class="bi bi-sliders"></i>
            </button>
        </div>

        <div class="search-main">
            <input type="text" class="search-input" id="news-search-input"
                placeholder="Search for companies, topics, or keywords...">
            <button class="search-button" id="search-button">
                <i class="bi bi-search"></i>
            </button>
        </div>

        <div class="advanced-filters" id="advanced-filters">
            <div class="filter-row">
                <div class="filter-group">
                    <label for="date-from">From Date</label>
                    <input type="date" id="date-from" class="form-control">
                </div>
                <div class="filter-group">
                    <label for="date-to">To Date</label>
                    <input type="date" id="date-to" class="form-control">
                </div>
                <div class="filter-group">
                    <label for="news-source">Source</label>
                    <select id="news-source" class="form-select">
                        <option value="">All Sources</option>
                        <option value="reuters">Reuters</option>
                        <option value="bloomberg">Bloomberg</option>
                        <option value="cnbc">CNBC</option>
                        <option value="marketwatch">MarketWatch</option>
                        <option value="yahoo">Yahoo Finance</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="sentiment-filter">Sentiment</label>
                    <select id="sentiment-filter" class="form-select">
                        <option value="">All Sentiments</option>
                        <option value="positive">Positive</option>
                        <option value="negative">Negative</option>
                        <option value="neutral">Neutral</option>
                    </select>
                </div>
            </div>
            <div class="filter-row">
                <div class="filter-group">
                    <button class="btn btn-primary" id="apply-filters">Apply Filters</button>
                </div>
                <div class="filter-group">
                    <button class="btn btn-outline-secondary" id="clear-filters">Clear All</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Featured News Section -->
    <div class="featured-news-section">
        <h5><i class="bi bi-star-fill text-warning"></i> Featured News</h5>
        <div class="featured-news-grid" id="featured-news-grid">
            <!-- All 6 featured articles will be loaded here in a 2x3 grid -->
            <div class="news-loading">
                <div class="news-loading-spinner"></div>
                <p>Loading featured news...</p>
            </div>
        </div>
    </div>

    <!-- More News Section -->
    <div class="more-news-section">
        <h5><i class="bi bi-newspaper"></i> More News</h5>
        <div class="news-list" id="more-news-list">
            <!-- More news articles will be loaded here -->
            <div class="news-loading">
                <div class="news-loading-spinner"></div>
                <p>Loading more news...</p>
            </div>
        </div>
    </div>
</div>


{% endblock %}

{% block scripts %}
<script type="module" src="{{ url_for('static', filename='js/news.js') }}"></script>
<script type="module" src="{{ url_for('static', filename='js/market-overview.js') }}"></script>
{% endblock %}