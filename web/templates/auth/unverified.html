{% extends "layout.html" %}

{% block title %}Email Verification Required - Market Monitor{% endblock %}

{% block head %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/auth.css') }}">
<style>
.verification-container {
    max-width: 600px;
    margin: 50px auto;
    padding: 0 20px;
}

.verification-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    padding: 40px;
    text-align: center;
    border: 1px solid #e5e7eb;
}

.verification-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 30px;
    background: linear-gradient(135deg, #fef3c7, #fbbf24);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 40px;
}

.verification-title {
    color: #1f2937;
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 15px;
}

.verification-subtitle {
    color: #6b7280;
    font-size: 16px;
    margin-bottom: 30px;
    line-height: 1.6;
}

.user-email {
    background: #f3f4f6;
    color: #374151;
    padding: 8px 16px;
    border-radius: 8px;
    font-weight: 600;
    display: inline-block;
    margin: 10px 0;
}

.verification-actions {
    margin: 30px 0;
    display: flex;
    flex-direction: column;
    gap: 15px;
    align-items: center;
}

.btn-resend {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-resend:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
    color: white;
    text-decoration: none;
}

.btn-resend:disabled {
    background: #9ca3af;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.btn-secondary {
    background: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
    padding: 10px 24px;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
}

.btn-secondary:hover {
    background: #e5e7eb;
    color: #374151;
    text-decoration: none;
}

.verification-info {
    background: #f0f9ff;
    border: 1px solid #bae6fd;
    border-radius: 12px;
    padding: 20px;
    margin: 30px 0;
    text-align: left;
}

.verification-info h4 {
    color: #0369a1;
    font-size: 16px;
    margin: 0 0 10px 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.verification-info ul {
    color: #0c4a6e;
    margin: 10px 0;
    padding-left: 20px;
}

.verification-info li {
    margin: 5px 0;
}

.resend-cooldown {
    color: #6b7280;
    font-size: 14px;
    margin-top: 10px;
}

.alert {
    padding: 15px;
    border-radius: 8px;
    margin: 20px 0;
}

.alert-success {
    background: #dcfce7;
    border: 1px solid #bbf7d0;
    color: #166534;
}

.alert-error {
    background: #fee2e2;
    border: 1px solid #fecaca;
    color: #991b1b;
}

@media (max-width: 768px) {
    .verification-container {
        margin: 20px auto;
        padding: 0 15px;
    }
    
    .verification-card {
        padding: 30px 20px;
    }
    
    .verification-title {
        font-size: 24px;
    }
    
    .verification-actions {
        flex-direction: column;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="verification-container">
    <div class="verification-card">
        <div class="verification-icon">
            📧
        </div>
        
        <h1 class="verification-title">Email Verification Required</h1>
        
        <p class="verification-subtitle">
            To access all features of Market Monitor, please verify your email address.
            We've sent a verification link to:
        </p>
        
        <div class="user-email">{{ current_user.email }}</div>
        
        <div class="verification-actions">
            <button id="resend-btn" class="btn-resend" onclick="resendVerification()">
                <i class="bi bi-arrow-clockwise"></i>
                Resend Verification Email
            </button>
            
            <a href="{{ url_for('auth.logout') }}" class="btn-secondary">
                <i class="bi bi-box-arrow-right"></i>
                Logout and Login with Different Account
            </a>
        </div>
        
        <div class="verification-info">
            <h4>
                <i class="bi bi-info-circle"></i>
                Why do I need to verify my email?
            </h4>
            <ul>
                <li>Ensures account security and prevents unauthorized access</li>
                <li>Enables you to receive important market alerts and summaries</li>
                <li>Allows password recovery if you forget your credentials</li>
                <li>Provides access to personalized email preferences</li>
            </ul>
        </div>
        
        <div id="resend-status" style="display: none;"></div>
        
        <p style="color: #6b7280; font-size: 14px; margin-top: 30px;">
            <i class="bi bi-shield-check"></i>
            Didn't receive the email? Check your spam folder or 
            <a href="mailto:<EMAIL>" style="color: #3b82f6;">contact support</a>
        </p>
    </div>
</div>

<script>
let resendCooldown = 0;
let cooldownInterval = null;

function resendVerification() {
    const btn = document.getElementById('resend-btn');
    const statusDiv = document.getElementById('resend-status');
    
    if (resendCooldown > 0) {
        return;
    }
    
    btn.disabled = true;
    btn.innerHTML = '<i class="bi bi-arrow-clockwise fa-spin"></i> Sending...';
    
    fetch('/auth/resend-verification', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        statusDiv.style.display = 'block';
        
        if (data.success) {
            statusDiv.innerHTML = '<div class="alert alert-success"><i class="bi bi-check-circle"></i> ' + data.message + '</div>';
            startCooldown();
        } else {
            statusDiv.innerHTML = '<div class="alert alert-error"><i class="bi bi-exclamation-triangle"></i> ' + data.error + '</div>';
            btn.disabled = false;
            btn.innerHTML = '<i class="bi bi-arrow-clockwise"></i> Resend Verification Email';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        statusDiv.style.display = 'block';
        statusDiv.innerHTML = '<div class="alert alert-error"><i class="bi bi-exclamation-triangle"></i> An error occurred. Please try again.</div>';
        btn.disabled = false;
        btn.innerHTML = '<i class="bi bi-arrow-clockwise"></i> Resend Verification Email';
    });
}

function startCooldown() {
    const btn = document.getElementById('resend-btn');
    resendCooldown = 60; // 60 seconds cooldown
    
    updateCooldownDisplay();
    
    cooldownInterval = setInterval(() => {
        resendCooldown--;
        updateCooldownDisplay();
        
        if (resendCooldown <= 0) {
            clearInterval(cooldownInterval);
            btn.disabled = false;
            btn.innerHTML = '<i class="bi bi-arrow-clockwise"></i> Resend Verification Email';
        }
    }, 1000);
}

function updateCooldownDisplay() {
    const btn = document.getElementById('resend-btn');
    if (resendCooldown > 0) {
        btn.innerHTML = `<i class="bi bi-clock"></i> Resend in ${resendCooldown}s`;
    }
}

// Auto-refresh page every 30 seconds to check if user was verified
setInterval(() => {
    fetch('/auth/api/user-info')
        .then(response => response.json())
        .then(data => {
            if (data.is_verified) {
                window.location.href = '/';
            }
        })
        .catch(error => console.log('Verification check failed:', error));
}, 30000);
</script>
{% endblock %}