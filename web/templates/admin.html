{% extends "layout.html" %}

{% block title %}Admin Dashboard - Market Monitor{% endblock %}

{% block head %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/auth.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/admin.css') }}">
{% endblock %}

{% block content %}
<div class="admin-container">
    <h2 style="color: #1f2937; font-weight: 700; margin-bottom: 30px; display: flex; align-items: center; gap: 10px;">
        <i class="bi bi-gear-fill" style="color: #3b82f6;"></i>
        Admin Dashboard
    </h2>

    <!-- Tab Navigation -->
    <div class="admin-tabs">
        <button class="admin-tab active" data-tab="general-tab">
            <i class="bi bi-gear"></i>
            General
        </button>
        <button class="admin-tab" data-tab="llm-playground-tab">
            <i class="bi bi-cpu"></i>
            LLM Playground
        </button>
        <button class="admin-tab" data-tab="users-tab">
            <i class="bi bi-people"></i>
            Users
        </button>
    </div>

    <!-- Alerts -->
    <div id="alert-container">
        <div id="success-alert" class="alert alert-success">
            <i class="bi bi-check-circle"></i>
            <span id="success-message"></span>
        </div>
        <div id="error-alert" class="alert alert-error">
            <i class="bi bi-exclamation-triangle"></i>
            <span id="error-message"></span>
        </div>
        <div id="info-alert" class="alert alert-info">
            <i class="bi bi-info-circle"></i>
            <span id="info-message"></span>
        </div>
    </div>

    <!-- General Tab Content -->
    <div id="general-tab" class="tab-content active">
        <!-- API Provider Management -->
        <div class="admin-section">
            <h3>
                <i class="bi bi-cpu"></i>
                API Provider Management
            </h3>
            <div class="admin-form">
                <div class="form-row">
                    <div class="form-group">
                        <label for="provider-name">Provider</label>
                        <select id="provider-name" name="provider-name">
                            <option value="">Select Provider</option>
                            <option value="openai">OpenAI</option>
                            <option value="anthropic">Anthropic</option>
                            <option value="gemini">Google Gemini</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="model-name">Model Name</label>
                        <input type="text" id="model-name" name="model-name" placeholder="e.g., gpt-4, claude-3-opus">
                    </div>
                    <div class="form-group">
                        <label for="display-name">Display Name</label>
                        <input type="text" id="display-name" name="display-name" placeholder="Human-readable name">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="description">Description</label>
                        <input type="text" id="description" name="description" placeholder="Optional description">
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="is-active" name="is-active" checked>
                            Active
                        </label>
                    </div>
                    <div class="form-group">
                        <button type="button" id="add-api-provider-btn" class="admin-btn">
                            <i class="bi bi-plus-circle"></i>
                            Add Provider/Model
                        </button>
                    </div>
                </div>
            </div>

            <!-- API Providers Table -->
            <div style="overflow-x: auto; margin-top: 20px;">
                <table class="users-table">
                    <thead>
                        <tr>
                            <th>Provider</th>
                            <th>Model</th>
                            <th>Display Name</th>
                            <th>Status</th>
                            <th>Default</th>
                            <th>Description</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="api-providers-table-body">
                        <!-- API providers will be loaded dynamically -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Market Analysis Controls -->
        <div class="admin-section">
            <h3>
                <i class="bi bi-graph-up"></i>
                Market Analysis Controls
            </h3>
            <div class="admin-form">
                <div class="form-row">
                    <div class="form-group">
                        <label for="api-provider">API Provider</label>
                        <select id="api-provider" name="api-provider">
                            <!-- Options will be populated dynamically from database -->
                        </select>
                    </div>
                    <div class="form-group">
                        <label>&nbsp;</label>
                        <button type="button" id="force-update-btn" class="admin-btn">
                            <i class="bi bi-arrow-clockwise"></i>
                            Force Update Analysis
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Email Management -->
        <div class="admin-section">
            <h3>
                <i class="bi bi-envelope"></i>
                Email Management
            </h3>
            <div class="admin-form">
                <div class="form-row">
                    <div class="form-group">
                        <label for="email-type">Email Type</label>
                        <select id="email-type" name="email-type">
                            <option value="daily_summary">Daily Market Summary</option>
                            <option value="market_alert">Market Alert</option>
                            <option value="test_email">Test Email</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="recipient-filter">Send To</label>
                        <select id="recipient-filter" name="recipient-filter">
                            <option value="selected">Selected Users</option>
                            <option value="all_active">All Active Users</option>
                            <option value="all_subscribed">All Subscribed Users</option>
                            <option value="admins_only">Admins Only</option>
                        </select>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <button type="button" id="send-email-btn" class="admin-btn">
                            <i class="bi bi-send"></i>
                            Send Email
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Prompt Management -->
        <div class="admin-section">
            <h3>
                <i class="bi bi-chat-text"></i>
                Prompt Management
            </h3>
            <div class="admin-form">
                <div class="form-row">
                    <div class="form-group">
                        <button type="button" id="refresh-prompts-btn" class="admin-btn">
                            <i class="bi bi-arrow-clockwise"></i>
                            Refresh Prompts
                        </button>
                    </div>
                    <div class="form-group">
                        <button type="button" id="create-prompt-btn" class="admin-btn">
                            <i class="bi bi-plus-circle"></i>
                            Create Prompt
                        </button>
                    </div>
                    <div class="form-group">
                        <input type="text" id="prompt-search" placeholder="Search prompts..." style="padding: 10px; border: 2px solid #e5e7eb; border-radius: 8px;">
                    </div>
                </div>
            </div>

            <!-- Prompts Table -->
            <div style="overflow-x: auto;">
                <table class="users-table">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Version</th>
                            <th>Active</th>
                            <th>Prod</th>
                            <th>Parser</th>
                            <th>Usage</th>
                            <th>Success Rate</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="prompts-table-body">
                        <!-- Prompts will be loaded dynamically -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- LLM Playground Tab Content -->
    <div id="llm-playground-tab" class="tab-content">
        <!-- API and Model Selection -->
        <div class="playground-section">
            <h4>
                <i class="bi bi-cpu"></i>
                API & Model Selection
            </h4>
            <div class="form-row">
                <div class="form-group">
                    <label for="playground-api-provider">API Provider</label>
                    <select id="playground-api-provider" name="playground-api-provider">
                        <option value="">Select API Provider</option>
                        <!-- Options will be populated from api_info table -->
                    </select>
                </div>
                <div class="form-group">
                    <label for="playground-model">Model</label>
                    <select id="playground-model" name="playground-model">
                        <option value="">Select Model</option>
                        <!-- Options will be populated based on selected provider -->
                    </select>
                </div>
                <div class="form-group">
                    <button type="button" id="load-prompt-btn" class="admin-btn">
                        <i class="bi bi-download"></i>
                        Load Prompt
                    </button>
                </div>
            </div>
        </div>

        <!-- Prompt Editor Section -->
        <div class="playground-section">
            <h4>
                <i class="bi bi-chat-text"></i>
                Market Prediction Prompt
                <button type="button" id="refresh-prompt-btn" class="admin-btn" style="margin-left: auto; padding: 8px 16px; font-size: 0.8rem;">
                    <i class="bi bi-arrow-clockwise"></i>
                    Refresh
                </button>
            </h4>
            
            <!-- Current Prompt Info -->
            <div id="prompt-info" style="margin-bottom: 20px;">
                <!-- Prompt info will be loaded dynamically -->
            </div>
            
            <!-- Prompt Editor -->
            <div class="form-row">
                <div class="form-group">
                    <label for="system-prompt-editor">System Prompt</label>
                    <textarea id="system-prompt-editor" class="prompt-editor" placeholder="System prompt will be loaded here..."></textarea>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label for="user-template-editor">User Template</label>
                    <textarea id="user-template-editor" class="prompt-editor" placeholder="User template will be loaded here..."></textarea>
                </div>
            </div>
        </div>

        <!-- Test LLM Section -->
        <div class="playground-section">
            <h4>
                <i class="bi bi-play-circle"></i>
                Generate Prediction
            </h4>
            <div class="test-request-section">
                <div class="form-row">
                    <div class="form-group">
                        <button type="button" id="test-llm-btn" class="admin-btn">
                            <i class="bi bi-send"></i>
                            Generate Prediction
                        </button>
                    </div>
                    <div class="form-group">
                        <button type="button" id="force-update-llm-btn" class="admin-btn" style="background: #f59e0b;">
                            <i class="bi bi-arrow-repeat"></i>
                            Force Update
                        </button>
                    </div>
                    <div class="form-group">
                        <button type="button" id="save-test-prompt-btn" class="admin-btn success">
                            <i class="bi bi-save"></i>
                            Save Test Prompt
                        </button>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>Prediction Result:</label>
                        <div id="test-result" class="test-response">
                            Prediction results will appear here...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Users Tab Content -->
    <div id="users-tab" class="tab-content">
        <!-- User Management -->
        <div class="admin-section">
            <h3>
                <i class="bi bi-people"></i>
                User Management
            </h3>
            <div class="admin-form">
                <div class="form-row">
                    <div class="form-group">
                        <button type="button" id="refresh-users-btn" class="admin-btn">
                            <i class="bi bi-arrow-clockwise"></i>
                            Refresh User List
                        </button>
                    </div>
                    <div class="form-group">
                        <button type="button" id="select-all-users-btn" class="admin-btn">
                            <i class="bi bi-check-all"></i>
                            Select All
                        </button>
                    </div>
                    <div class="form-group">
                        <button type="button" id="deselect-all-users-btn" class="admin-btn">
                            <i class="bi bi-x-square"></i>
                            Deselect All
                        </button>
                    </div>
                </div>
            </div>

            <!-- Users Table -->
            <div style="overflow-x: auto;">
                <table class="users-table">
                    <thead>
                        <tr>
                            <th><input type="checkbox" id="select-all-checkbox" class="user-checkbox"></th>
                            <th>Username</th>
                            <th>Email</th>
                            <th>Name</th>
                            <th>Status</th>
                            <th>Verified</th>
                            <th>Role</th>
                            <th>Created</th>
                            <th>Last Login</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="users-table-body">
                        <!-- Users will be loaded dynamically -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Loading overlay -->
<div class="loading-overlay" id="loading-overlay">
    <div class="loading-spinner"></div>
</div>

<script src="{{ url_for('static', filename='js/admin.js') }}"></script>
{% endblock %}