<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Market Prediction Summary</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: #1f2937;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
        }

        .email-container {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 30px 20px;
        }

        .header h1 {
            margin: 0 0 8px 0;
            font-size: 28px;
            font-weight: 700;
        }

        .header .subtitle {
            font-size: 16px;
            opacity: 0.9;
            margin: 0;
            font-weight: 400;
        }

        .content {
            padding: 0;
        }

        .section {
            padding: 24px;
            border-bottom: 1px solid #f1f5f9;
        }

        .section:last-of-type {
            border-bottom: none;
        }

        .section-title {
            color: #1e293b;
            margin: 0 0 16px 0;
            font-size: 18px;
            font-weight: 600;
        }

        .prediction-value {
            font-size: 24px;
            font-weight: 700;
            color: #047857;
            margin-bottom: 16px;
            text-align: center;
            padding: 16px;
            background: linear-gradient(135deg, #d1fae5, #a7f3d0);
            border-radius: 8px;
        }

        .evidence-section {
            background: #f8fafc;
            border-radius: 8px;
            padding: 16px;
            border-left: 4px solid #10b981;
            margin-bottom: 16px;
        }

        .evidence-title {
            font-size: 14px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 12px;
        }

        .evidence-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .evidence-item {
            font-size: 14px;
            color: #4b5563;
            line-height: 1.5;
            margin-bottom: 8px;
            padding-left: 16px;
            position: relative;
        }

        .evidence-item::before {
            content: '•';
            color: #10b981;
            position: absolute;
            left: 0;
            font-weight: bold;
        }

        .market-theme {
            background: linear-gradient(135deg, #fef3c7, #fed7aa);
            border-radius: 8px;
            padding: 16px;
            border: 1px solid #f59e0b;
        }

        .theme-title {
            font-size: 14px;
            font-weight: 600;
            color: #92400e;
            margin-bottom: 8px;
        }

        .theme-content {
            font-size: 14px;
            color: #78350f;
            font-weight: 500;
        }

        /* CTA Section */
        .cta-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            text-align: center;
            padding: 40px 30px;
        }

        .cta-button {
            display: inline-block;
            background: white;
            color: #667eea;
            padding: 16px 32px;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 700;
            font-size: 16px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        /* Footer */
        .footer {
            background: #f8fafc;
            text-align: center;
            padding: 30px;
            color: #64748b;
            font-size: 13px;
            border-top: 1px solid #e2e8f0;
        }

        .footer a {
            color: #3b82f6;
            text-decoration: none;
            font-weight: 500;
        }

        .footer a:hover {
            text-decoration: underline;
        }

        .footer-links {
            margin: 16px 0;
        }

        .footer-links a {
            margin: 0 8px;
        }
    </style>
</head>

<body>
    <div class="email-container">
        <div class="header">
            <h1>📈 Market Monitor</h1>
            <p class="subtitle">Daily Market Summary for {{ data.date.strftime('%B %d, %Y') }}</p>
            <p class="greeting">Hello {{ user.first_name or user.username }}!</p>
        </div>

        <div class="content">
            {% if data.error %}
            <div class="section">
                <p>Service temporarily unavailable. Please visit our website for the latest market information.</p>
            </div>
            {% else %}

            <!-- Market Prediction -->
            {% if data.llm_prediction and data.llm_prediction.prediction %}
            <div class="section">
                <div class="section-title">🔮 Market Prediction</div>
                <div class="prediction-value">
                    {{ data.llm_prediction.prediction.upper() }}
                    {% if data.llm_prediction.confidence %}
                    <div style="font-size: 16px; margin-top: 8px; color: #047857;">
                        {{ "%.1f"|format(data.llm_prediction.confidence * 100) }}% Confidence
                    </div>
                    {% endif %}
                </div>
            </div>
            {% endif %}

            <!-- Key Evidence -->
            {% if data.llm_prediction and data.llm_prediction.key_evidence and data.llm_prediction.key_evidence|length > 0 %}
            <div class="section">
                <div class="section-title">💡 Key Evidence</div>
                <div class="evidence-section">
                    <ul class="evidence-list">
                        {% for evidence in data.llm_prediction.key_evidence[:5] %}
                        <li class="evidence-item">
                            {{ evidence.fact }}
                            {% if evidence.url and evidence.url != 'N/A' and evidence.article_ref != 'MARKET' %}
                            <div style="margin-top: 6px;">
                                <a href="{{ evidence.url }}" style="color: #3b82f6; text-decoration: none; font-size: 13px; font-weight: 500;">📖 Read article</a>
                            </div>
                            {% endif %}
                        </li>
                        {% endfor %}
                    </ul>
                </div>
            </div>
            {% endif %}

            <!-- Dominant Market Theme -->
            {% if data.llm_prediction and data.llm_prediction.dominant_theme and data.llm_prediction.dominant_theme.theme %}
            <div class="section">
                <div class="section-title">🎯 Dominant Market Theme</div>
                <div class="market-theme">
                    <div class="theme-content">
                        {{ data.llm_prediction.dominant_theme.theme }}
                    </div>
                    {% if data.llm_prediction.dominant_theme.supporting_articles and data.llm_prediction.dominant_theme.supporting_articles|length > 0 %}
                    <div style="margin-top: 12px; padding-top: 12px; border-top: 1px solid rgba(146, 64, 14, 0.2);">
                        <div style="font-size: 12px; font-weight: 600; color: #92400e; margin-bottom: 8px;">📚 Supporting Articles</div>
                        {% for article in data.llm_prediction.dominant_theme.supporting_articles[:3] %}
                        <div style="margin-bottom: 6px;">
                            {% if article.url and article.url != 'N/A' %}
                            <a href="{{ article.url }}" style="color: #3b82f6; text-decoration: none; font-size: 13px; font-weight: 500;">{{ article.article_title or 'Article' }}</a>
                            <span style="color: #a16207; font-size: 11px; margin-left: 4px;">({{ article.source or 'Unknown' }})</span>
                            {% else %}
                            <span style="font-weight: 500; font-size: 13px; color: #78350f;">{{ article.article_title or 'Article' }}</span>
                            <span style="color: #a16207; font-size: 11px; margin-left: 4px;">({{ article.source or 'Unknown' }})</span>
                            {% endif %}
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>
            </div>
            {% endif %}

            {% endif %}
        </div>

        <!-- Call to Action -->
        <div class="cta-section">
            <a href="{{ url_for('index', _external=True) }}" class="cta-button">
                View Full Dashboard
            </a>
        </div>  

        <!-- Footer -->
        <div class="footer">
            <p>
                This email was sent to {{ user.email }} because you subscribed to daily market summaries.
            </p>
            <div class="footer-links">
                <a href="{{ url_for('auth.profile', _external=True) }}">Update Preferences</a>
                <a href="{{ url_for('auth.profile', _external=True) }}">Unsubscribe</a>
            </div>
            <p>
                © {{ data.date.year }} Market Monitor. All rights reserved.
            </p>
        </div>
    </div>
</body>

</html>