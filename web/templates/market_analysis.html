{% extends "layout.html" %}

{% block title %}Market Analysis - Market Monitor{% endblock %}

{% block head %}
<!-- Plotly.js for charts -->
<script src="https://cdn.plot.ly/plotly-3.0.1.min.js"></script>
<!-- Additional CSS for Market Analysis -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/market-outlook.css') }}">
<style>
    .market-analysis-container {
        padding: 20px 0;
    }
            
    .ai-analysis-section {
        background: white;
        border-radius: 12px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    
    .prediction-header {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-bottom: 20px;
    }
    
    .confidence-display {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }
    
    .evidence-container {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
        max-height: none;
        overflow: visible;
    }
    
    .evidence-item {
        background: white;
        border-radius: 6px;
        padding: 12px;
        margin-bottom: 10px;
        border-left: 4px solid #007bff;
    }
    
    .evidence-item:last-child {
        margin-bottom: 0;
    }
    
    .evidence-fact {
        color: #000000 !important;
        font-weight: 600 !important;
        line-height: 1.5;
        margin-bottom: 8px;
        background: #ffffff !important;
        padding: 8px !important;
        border: 1px solid #333333 !important;
        border-radius: 4px !important;
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
    }
    
    .article-reference {
        font-size: 0.9em;
        color: #6c757d;
        margin-top: 5px;
    }
    
    .article-reference a {
        color: #007bff;
        text-decoration: none;
    }
    
    .article-reference a:hover {
        text-decoration: underline;
    }
    
    .themes-section {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }
    
    .technical-indicators {
        background: #e7f3ff;
        border: 1px solid #b3d9ff;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }
    
    .loading-spinner {
        text-align: center;
        padding: 40px;
    }
    
    .error-message {
        background: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container market-analysis-container">
    <div class="row">
        <div class="col-12 mb-3">
            <h4 class="mb-0 text-secondary d-flex align-items-center">
                <div class="bg-gradient p-1 rounded me-2" style="background: linear-gradient(45deg, #667eea, #764ba2) !important;">
                    <i class="bi bi-graph-up text-white"></i>
                </div>
                AI-Powered Market Insights
            </h4>
        </div>
    </div>
    <!-- Interactive Chart -->
    <div class="row">
        <div class="col-12">
            <div class="chart-container">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <div class="d-flex align-items-center">
                        <div id="live-indicator" class="mb-0 ms-1"></div>
                        <h5 id="price-summary-container" class="mb-0 ms-1">
                            <span>Loading price summary...</span>
                        </h5>
                    </div>
                </div>
                
                <div class="d-flex justify-content-between align-items-start align-items-md-center mb-3">
                    <div id="ticker-selector" class="m-0"></div>
                    <div id="time-range-selector" class="m-0"></div>
                    <div id="chart-type-selector" class="m-0"></div>
                </div>
                
                <div id="price-chart">
                    <div class="loading-spinner">
                        <div class="spinner-border spinner-border-sm text-primary" role="status">
                            <span class="visually-hidden">Loading chart...</span>
                        </div>
                        <p class="mt-1 small">Loading price chart...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- AI Analysis Section -->
    <div class="row">
        <div class="col-12">
            <div class="ai-analysis-section">
                <div class="prediction-header">
                    <h5><i class="bi bi-robot"></i> AI Market Analysis</h5>
                </div>

                <div id="ai-analysis-content">
                    <div class="loading-spinner">
                        <div class="spinner-border text-success" role="status">
                            <span class="visually-hidden">Loading AI analysis...</span>
                        </div>
                        <p class="mt-2">Generating AI market analysis...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- Import utilities -->
<script type="module" src="{{ url_for('static', filename='js/utils.js') }}"></script>
<!-- Price Graph Module (must be loaded first) -->
<script type="module" src="{{ url_for('static', filename='js/price-graph.js') }}"></script>
<!-- Market Analysis Module -->
<script type="module" src="{{ url_for('static', filename='js/market-analysis.js') }}"></script>
{% endblock %}
