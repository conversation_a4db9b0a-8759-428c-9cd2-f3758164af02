/**
 * News Section Styles
 */

/* Clickable News Elements */
.clickable-news-image,
.clickable-news-title {
    cursor: pointer !important;
    transition: all 0.2s ease;
}

.clickable-news-image:hover {
    opacity: 0.8;
    transform: scale(1.02);
}

.clickable-news-title:hover {
    color: #007bff !important;
    text-decoration: underline;
}

.featured-article .clickable-news-title:hover {
    color: #007bff !important;
}

.news-list-item .clickable-news-title:hover {
    color: #007bff !important;
}

/* Focus styles for keyboard accessibility */
.clickable-news-image:focus,
.clickable-news-title:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
    border-radius: 4px;
}

.clickable-news-image:focus {
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
}

.clickable-news-title:focus {
    background-color: rgba(0, 123, 255, 0.1);
}

/* Market Overview Strip */
.market-overview {
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
    color: white;
    padding: 6px 0;
    margin: 0;
    width: 100vw;
    margin-left: calc(-50vw + 50%);
    border-bottom: 1px solid #e5e7eb;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.market-ticker {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.market-ticker::-webkit-scrollbar {
    display: none;
}

.ticker-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 65px;
    flex-shrink: 0;
    padding: 2px 8px;
}

.ticker-label {
    font-size: 0.65rem;
    opacity: 0.9;
    margin-bottom: 1px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.ticker-value {
    font-size: 0.8rem;
    font-weight: 700;
    margin-bottom: 1px;
    line-height: 1;
}

.ticker-change {
    font-size: 0.6rem;
    font-weight: 600;
    line-height: 1;
}

.ticker-change.positive {
    color: #4ade80;
}

.ticker-change.negative {
    color: #f87171;
}

.market-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
}

.metric-item {
    background: rgba(255, 255, 255, 0.1);
    padding: 15px;
    border-radius: 8px;
    text-align: center;
    backdrop-filter: blur(10px);
}

.metric-label {
    font-size: 0.85rem;
    opacity: 0.9;
    margin-bottom: 5px;
    font-weight: 500;
}

.metric-value {
    font-size: 1.2rem;
    font-weight: 700;
    margin-bottom: 3px;
}

.metric-change {
    font-size: 0.8rem;
    font-weight: 600;
}

.metric-change.positive {
    color: #4ade80;
}

.metric-change.negative {
    color: #f87171;
}

/* Search Section */
.search-section {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 25px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    border: 1px solid #e5e7eb;
}

.search-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.search-main {
    position: relative;
    margin-bottom: 15px;
}

.search-input {
    width: 100%;
    padding: 12px 45px 12px 15px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 0.85rem;
    transition: border-color 0.3s ease;
}

.search-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-button {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    background: #3b82f6;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.search-button:hover {
    background: #2563eb;
}

.advanced-filters {
    display: none;
    padding-top: 15px;
    border-top: 1px solid #e5e7eb;
    margin-top: 15px;
}

.advanced-filters.show {
    display: block;
}

.filter-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 15px;
}

.filter-group {
    display: flex;
    flex-direction: column;
}

.filter-group label {
    font-size: 0.9rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 5px;
}

.filter-group select,
.filter-group input {
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 0.9rem;
}

.toggle-advanced {
    background: none;
    border: none;
    color: #3b82f6;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
}

.toggle-advanced:hover {
    color: #2563eb;
}

/* Featured News Section */
.featured-news-section {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 25px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    border: 1px solid #e5e7eb;
}

.featured-news-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(2, 1fr);
    gap: 20px;
    margin-bottom: 20px;
}

.featured-article {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: 1px solid #e5e7eb;
    cursor: pointer;
}

.featured-article:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.featured-article-image {
    width: 100%;
    height: 180px;
    object-fit: cover;
    background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
}

.featured-article {
    height: 100%;
    min-height: 350px;
}

.featured-article-title {
    font-size: 1rem;
    line-height: 1.3;
    margin-bottom: 10px;
    font-weight: 600;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.featured-article-summary {
    font-size: 0.85rem;
    line-height: 1.4;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    margin-bottom: 12px;
}

.featured-article-content {
    padding: 15px;
    display: flex;
    flex-direction: column;
    height: calc(100% - 180px);
}

/* Responsive Grid */
@media (max-width: 992px) {
    .featured-news-grid {
        grid-template-columns: repeat(2, 1fr);
        grid-template-rows: repeat(3, 1fr);
    }
}

@media (max-width: 768px) {
    .featured-news-grid {
        grid-template-columns: 1fr;
        grid-template-rows: repeat(6, 1fr);
        gap: 15px;
    }
    
    .featured-article {
        min-height: 280px;
    }
    
    .featured-article-image {
        height: 140px;
    }
    
    .featured-article-content {
        padding: 12px;
        height: calc(100% - 140px);
    }
    
    .featured-article-title {
        font-size: 0.95rem;
        line-height: 1.25;
        -webkit-line-clamp: 3;
        line-clamp: 3;
        margin-bottom: 8px;
    }
    
    .featured-article-summary {
        font-size: 0.8rem;
        line-height: 1.3;
        -webkit-line-clamp: 3;
        line-clamp: 3;
        margin-bottom: 8px;
    }
    
    
    .featured-article-source {
        font-size: 0.75rem;
        font-weight: 600;
    }
    
    .news-list-date {
        font-size: 0.7rem;
    }
    
    .sentiment-badge {
        align-self: flex-start;
        font-size: 0.65rem;
        padding: 2px 6px;
    }
}

@media (max-width: 576px) {
    .featured-article {
        min-height: 260px;
    }
    
    .featured-article-image {
        height: 120px;
    }
    
    .featured-article-content {
        padding: 10px;
        height: calc(100% - 120px);
    }
    
    .featured-article-title {
        font-size: 0.9rem;
        line-height: 1.2;
    }
    
    .featured-article-summary {
        font-size: 0.75rem;
        line-height: 1.25;
    }
}

/* Influence Tagging Styles */
.influence-tags {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 10px;
    margin-top: 10px;
    font-size: 0.85rem;
}

.influence-tags-list {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    margin-bottom: 8px;
}

.influence-tag {
    background: #dee6f4;
    color: rgb(29, 26, 26);
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
}

.influence-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;
    font-size: 0.8rem;
    color: #6b7280;
}

.influence-score {
    font-weight: 700;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.8rem;
}

.influence-score.positive {
    background: #dcfce7;
    color: #166534;
}

.influence-score.negative {
    background: #fee2e2;
    color: #991b1b;
}

.influence-score.neutral {
    background: #f3f4f6;
    color: #374151;
}

.influence-score.has-tooltip {
    position: relative;
    cursor: help;
}

.influence-score.has-tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-100%);
    background: #a0aab9;
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 0.75rem;
    white-space: pre-wrap;
    max-width: 600px;
    min-width: 200px;
    z-index: 1000;
    opacity: 50;
    visibility: hidden;
    transition: opacity 0.3s ease;
    margin-bottom: 5px;
    text-align: left;
    line-height: 1.4;
}

.influence-score.has-tooltip:hover::after {
    opacity: 1;
    visibility: visible;
}

.featured-article-content {
    padding: 20px;
}

.featured-article-title {
    font-size: 1.1rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 10px;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.featured-article-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    font-size: 0.85rem;
    color: #6b7280;
}

.featured-article-source {
    font-weight: 600;
    color: #3b82f6;
}

.featured-article-summary {
    color: #4b5563;
    line-height: 1.5;
    font-size: 0.9rem;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.sentiment-badge {
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
}

.sentiment-positive {
    background: #dcfce7;
    color: #166534;
}

.sentiment-negative {
    background: #fee2e2;
    color: #991b1b;
}

.sentiment-neutral {
    background: #f3f4f6;
    color: #374151;
}

/* More News Section */
.more-news-section {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 25px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    border: 1px solid #e5e7eb;
}


.news-list {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    border: 1px solid #e5e7eb;
}

.news-list-item {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #f3f4f6;
    transition: background-color 0.3s ease;
    cursor: pointer;
}

.news-list-item:last-child {
    border-bottom: none;
}

.news-list-item:hover {
    background: #f9fafb;
}

.news-list-image {
    width: 100px;
    height: 80px;
    object-fit: cover;
    border-radius: 6px;
    margin-right: 15px;
    background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
    flex-shrink: 0;
}

.news-list-content {
    flex: 1;
    min-width: 0;
}

.news-list-title {
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 5px;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.news-list-meta {
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 0.8rem;
    color: #6b7280;
    margin-bottom: 8px;
}

.news-list-source {
    font-weight: 600;
    color: #3b82f6;
}

.news-list-summary {
    font-size: 0.85rem;
    font-weight: 500;
    color: #4b5563;
    line-height: 1.5;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.news-list-date {
    font-size: 0.75rem;
    color: #9ca3af;
    font-weight: 500;
}

/* Infinite Scroll */
.load-more-container {
    text-align: center;
    padding: 20px;
    border-top: 1px solid #f3f4f6;
}

.load-more-btn {
    background: #3b82f6;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.load-more-btn:hover {
    background: #2563eb;
}

.load-more-btn:disabled {
    background: #9ca3af;
    cursor: not-allowed;
}

.loading-more {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    color: #6b7280;
    font-size: 0.9rem;
}

.loading-more .spinner {
    width: 16px;
    height: 16px;
    border: 2px solid #e5e7eb;
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Loading and Error States */
.news-loading {
    text-align: center;
    padding: 40px;
    color: #6b7280;
}

.news-loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f4f6;
    border-top: 4px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.news-error {
    background: #fee2e2;
    border: 1px solid #fecaca;
    color: #991b1b;
    padding: 20px;
    border-radius: 8px;
    text-align: center;
}

/* Responsive Design */
@media (max-width: 768px) {
    .market-metrics {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .featured-top-row {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .main-featured-article .featured-article-image {
        height: 200px;
    }

    .right-side-featured-container {
        grid-template-rows: none;
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }

    .bottom-featured-container {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .filter-row {
        grid-template-columns: 1fr;
    }
        
    .news-list-item {
        flex-direction: column;
        align-items: flex-start;
        text-align: left;
    }
    
    .news-list-image {
        width: 100%;
        height: 150px;
        margin-right: 0;
        margin-bottom: 10px;
    }
}

/* Compact Market Overview Mobile Responsive */
@media (max-width: 768px) {
    .compact-market-overview {
        padding: 6px 0;
        margin-bottom: 15px;
    }
    
    .market-ticker {
        gap: 10px;
        padding: 0 10px;
    }
    
    .ticker-item {
        min-width: 60px;
    }
    
    .ticker-label {
        font-size: 0.65rem;
    }
    
    .ticker-value {
        font-size: 0.8rem;
    }
    
    .ticker-change {
        font-size: 0.6rem;
    }
}

@media (max-width: 576px) {
    .compact-market-overview {
        padding: 4px 0;
        margin-bottom: 12px;
    }
    
    .market-ticker {
        gap: 8px;
        padding: 0 8px;
    }
    
    .ticker-item {
        min-width: 55px;
    }
    
    .ticker-label {
        font-size: 0.6rem;
        margin-bottom: 1px;
    }
    
    .ticker-value {
        font-size: 0.75rem;
        margin-bottom: 0;
    }
    
    .ticker-change {
        font-size: 0.55rem;
    }
}

@media (max-width: 480px) {
    .market-metrics {
        grid-template-columns: 1fr;
    }
    
    .search-section {
        padding: 15px;
    }
    
    .featured-news-section {
        margin-bottom: 30px;
    }

    .featured-article-content {
        padding: 15px;
    }

    .more-news-section {
        margin-bottom: 20px;
        color: #1f2937;
        font-weight: 700;
    }

    .compact-market-overview {
        padding: 3px 0;
        margin-bottom: 10px;
    }
    
    .market-ticker {
        gap: 6px;
        padding: 0 6px;
        justify-content: space-around;
    }
    
    .ticker-item {
        min-width: 50px;
    }
    
    .ticker-label {
        font-size: 0.55rem;
        margin-bottom: 1px;
    }
    
    .ticker-value {
        font-size: 0.7rem;
        margin-bottom: 0;
    }
    
    .ticker-change {
        font-size: 0.5rem;
    }
}

/* Mobile styles for news meta - moved from JavaScript */
@media (max-width: 768px) {
    .news-list-meta {
        display: flex;
        flex-direction: column;
        gap: 4px;
    }
                
    .meta-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 2px;
        margin-top: 2px;
    }
    
    .meta-tags span {
        font-size: 10px;
        background: #e2e8f0;
        color: #475569;
        padding: 1px 4px;
        border-radius: 3px;
        max-width: 80px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        border: 1px solid #cbd5e1;
    }
    
    .news-list-source {
        font-weight: 600;
        color: #3b82f6;
    }
    
    .news-list-date {
        color: #64748b;
        font-size: 11px;
    }
    
    .meta-separator {
        color: #94a3b8;
        font-size: 12px;
    }
}

/* Desktop styles for news meta - moved from JavaScript */
@media (min-width: 769px) {    
    .news-meta-primary {
        display: flex;
        align-items: center;
        gap: 6px;
    }
    
    .meta-separator {
        color: #94a3b8;
        font-size: 12px;
    }
    
    .meta-category,
    .meta-regions,
    .meta-tags {
        font-size: 12px;
        color: #64748b;
    }
    
    .meta-tags span {
        margin-right: 4px;
    }
}
