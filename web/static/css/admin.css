.admin-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.admin-section {
    background: white;
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    border: 1px solid #e5e7eb;
}

.admin-section h3 {
    color: #1f2937;
    font-weight: 700;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.admin-section h3 i {
    color: #3b82f6;
}

.admin-form {
    display: grid;
    gap: 20px;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
    font-size: 0.9rem;
}

.form-group select,
.form-group input {
    padding: 10px 12px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 0.9rem;
    transition: border-color 0.3s ease;
}

.form-group select:focus,
.form-group input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.admin-btn {
    background: #3b82f6;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.admin-btn:hover {
    background: #2563eb;
}

.admin-btn:disabled {
    background: #9ca3af;
    cursor: not-allowed;
}

.admin-btn.danger {
    background: #ef4444;
}

.admin-btn.danger:hover {
    background: #dc2626;
}

.admin-btn.success {
    background: #10b981;
}

.admin-btn.success:hover {
    background: #059669;
}

.users-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

.users-table th,
.users-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #e5e7eb;
}

.users-table th {
    background: #f9fafb;
    font-weight: 600;
    color: #374151;
}

.users-table tbody tr:hover {
    background: #f9fafb;
}

.user-checkbox {
    width: 16px;
    height: 16px;
}

.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-active {
    background: #10b981;
}

.status-inactive {
    background: #ef4444;
}

.admin-badge {
    background: #3b82f6;
    color: white;
    font-size: 0.75rem;
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: 600;
}

.verified-badge {
    background: #10b981;
    color: white;
    font-size: 0.75rem;
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: 600;
}

.unverified-badge {
    background: #ef4444;
    color: white;
    font-size: 0.75rem;
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: 600;
}

.btn-verify {
    background: #10b981;
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.75rem;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.btn-verify:hover {
    background: #059669;
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f4f6;
    border-top: 4px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.alert {
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    display: none;
}

.alert-success {
    background: #dcfce7;
    border: 1px solid #bbf7d0;
    color: #166534;
}

.alert-error {
    background: #fee2e2;
    border: 1px solid #fecaca;
    color: #991b1b;
}

.alert-info {
    background: #dbeafe;
    border: 1px solid #bfdbfe;
    color: #1e40af;
}

/* Tab navigation styles */
.admin-tabs {
    display: flex;
    border-bottom: 2px solid #e5e7eb;
    margin-bottom: 30px;
}

.admin-tab {
    background: none;
    border: none;
    padding: 15px 25px;
    color: #6b7280;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    border-bottom: 2px solid transparent;
}

.admin-tab:hover {
    color: #3b82f6;
    background: #f9fafb;
}

.admin-tab.active {
    color: #3b82f6;
    border-bottom-color: #3b82f6;
    background: #f9fafb;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* LLM Playground specific styles */
.playground-section {
    background: white;
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    border: 1px solid #e5e7eb;
}

.playground-section h4 {
    color: #1f2937;
    font-weight: 600;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.current-settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.setting-item {
    background: #f9fafb;
    padding: 12px;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
}

.setting-label {
    font-size: 0.8rem;
    color: #6b7280;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.setting-value {
    font-size: 0.9rem;
    color: #1f2937;
    font-weight: 600;
    margin-top: 4px;
}

.prompt-editor {
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    padding: 15px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.85rem;
    line-height: 1.5;
    resize: vertical;
    min-height: 200px;
    transition: border-color 0.3s ease;
}

.prompt-editor:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.test-request-section {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 20px;
    margin-top: 20px;
}

.test-response {
    background: #1f2937;
    color: #f9fafb;
    padding: 15px;
    border-radius: 8px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.85rem;
    line-height: 1.5;
    white-space: pre-wrap;
    overflow-x: auto;
    max-height: 400px;
    overflow-y: auto;
}

.test-response.error {
    background: #fee2e2;
    color: #991b1b;
    border: 1px solid #fecaca;
}

.test-response.success {
    background: #dcfce7;
    color: #166534;
    border: 1px solid #bbf7d0;
}

@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .admin-container {
        padding: 15px;
    }
    
    .admin-section {
        padding: 20px;
    }
    
    .users-table {
        font-size: 0.85rem;
    }
    
    .users-table th,
    .users-table td {
        padding: 8px;
    }
    
    .admin-tabs {
        overflow-x: auto;
    }
    
    .admin-tab {
        white-space: nowrap;
        min-width: 120px;
    }
    
    .current-settings-grid {
        grid-template-columns: 1fr;
    }
}