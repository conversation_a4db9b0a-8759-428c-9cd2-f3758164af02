class AdminDashboard {
    constructor() {
        this.selectedUsers = new Set();
        this.users = [];
        this.apiProviders = [];
        this.defaultApiInfo = null;
        this.prompts = [];
        this.currentSettings = {};
        this.currentPrompt = null;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupTabs();
        this.loadUsers();
        this.loadApiProviders();
        this.loadPrompts();
        this.loadCurrentSettings();
    }

    setupTabs() {
        const tabs = document.querySelectorAll('.admin-tab');
        const tabContents = document.querySelectorAll('.tab-content');

        tabs.forEach(tab => {
            tab.addEventListener('click', () => {
                // Remove active class from all tabs and contents
                tabs.forEach(t => t.classList.remove('active'));
                tabContents.forEach(content => content.classList.remove('active'));

                // Add active class to clicked tab and corresponding content
                tab.classList.add('active');
                const targetContent = document.getElementById(tab.dataset.tab);
                if (targetContent) {
                    targetContent.classList.add('active');
                }
            });
        });

        // Set first tab as active by default
        if (tabs.length > 0) {
            tabs[0].click();
        }
    }

    setupEventListeners() {
        // API Provider management
        document.getElementById('add-api-provider-btn')?.addEventListener('click', () => {
            this.addApiProvider();
        });

        // Force update analysis
        document.getElementById('force-update-btn')?.addEventListener('click', () => {
            this.forceUpdateAnalysis();
        });

        // Send email
        document.getElementById('send-email-btn')?.addEventListener('click', () => {
            this.sendEmail();
        });

        // Prompt management
        document.getElementById('refresh-prompts-btn')?.addEventListener('click', () => {
            this.loadPrompts();
        });

        document.getElementById('create-prompt-btn')?.addEventListener('click', () => {
            this.showCreatePromptModal();
        });

        document.getElementById('prompt-search')?.addEventListener('input', (e) => {
            this.filterPrompts(e.target.value);
        });

        // User management
        document.getElementById('refresh-users-btn')?.addEventListener('click', () => {
            this.loadUsers();
        });

        document.getElementById('select-all-users-btn')?.addEventListener('click', () => {
            this.selectAllUsers();
        });

        document.getElementById('deselect-all-users-btn')?.addEventListener('click', () => {
            this.deselectAllUsers();
        });

        // Master checkbox
        document.getElementById('select-all-checkbox')?.addEventListener('change', (e) => {
            if (e.target.checked) {
                this.selectAllUsers();
            } else {
                this.deselectAllUsers();
            }
        });

        // LLM Playground
        document.getElementById('playground-api-provider')?.addEventListener('change', () => {
            this.updatePlaygroundModels();
        });

        document.getElementById('load-prompt-btn')?.addEventListener('click', () => {
            this.loadMarketPredictionPrompt();
        });

        document.getElementById('refresh-prompt-btn')?.addEventListener('click', () => {
            this.loadMarketPredictionPrompt();
        });

        document.getElementById('test-llm-btn')?.addEventListener('click', () => {
            this.testLLMPrediction();
        });

        document.getElementById('force-update-llm-btn')?.addEventListener('click', () => {
            this.testLLMPrediction(true);
        });

        document.getElementById('save-test-prompt-btn')?.addEventListener('click', () => {
            this.saveTestPrompt();
        });
    }

    showLoading() {
        document.getElementById('loading-overlay').style.display = 'flex';
    }

    hideLoading() {
        document.getElementById('loading-overlay').style.display = 'none';
    }

    showAlert(type, message) {
        // Hide all alerts first
        document.querySelectorAll('.alert').forEach(alert => alert.style.display = 'none');
        
        // Show the specific alert
        const alert = document.getElementById(`${type}-alert`);
        const messageElement = document.getElementById(`${type}-message`);
        
        if (alert && messageElement) {
            messageElement.textContent = message;
            alert.style.display = 'block';
            
            // Auto-hide after 5 seconds
            setTimeout(() => {
                alert.style.display = 'none';
            }, 5000);
        }
    }

    // LLM Playground Methods
    async loadCurrentSettings() {
        try {
            this.showLoading();
            
            // Load API info and prompt data
            await Promise.all([
                this.loadPlaygroundApiInfo(),
                this.loadMarketPredictionPrompt()
            ]);
            
        } catch (error) {
            this.showAlert('error', 'Error loading playground data: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }

    async loadPlaygroundApiInfo() {
        try {
            const response = await fetch('/api/admin/api-providers');
            const data = await response.json();
            
            if (response.ok) {
                this.playgroundApiInfo = data.api_info_list;
                this.defaultApiInfo = data.default_api_info;
                this.populateApiProviderDropdown(data.providers);
                this.showAlert('info', `Loaded ${data.api_info_list.length} API configurations`);
            } else {
                this.showAlert('error', data.error || 'Failed to load API info');
            }
        } catch (error) {
            this.showAlert('error', 'Error loading API info: ' + error.message);
        }
    }

    populateApiProviderDropdown(providers) {
        const dropdown = document.getElementById('playground-api-provider');
        if (!dropdown) return;
        
        dropdown.innerHTML = '<option value="">Select API Provider</option>';

        // Add options grouped by provider
        Object.keys(providers).forEach(providerName => {
            const option = document.createElement('option');
            option.value = providerName;
            option.textContent = providerName.charAt(0).toUpperCase() + providerName.slice(1);
            if (this.defaultApiInfo && this.defaultApiInfo.provider_name === providerName) {
                option.selected = true;
            }
            dropdown.appendChild(option);
        });

        // Update models for selected provider
        this.updatePlaygroundModels();
    }

    updatePlaygroundModels() {
        const providerDropdown = document.getElementById('playground-api-provider');
        const modelDropdown = document.getElementById('playground-model');
        
        if (!providerDropdown || !modelDropdown) return;
        
        const selectedProvider = providerDropdown.value;
        modelDropdown.innerHTML = '<option value="">Select Model</option>';
        
        if (selectedProvider && this.playgroundApiInfo) {
            // Find all models for the selected provider
            const providerModels = this.playgroundApiInfo.filter(
                api => api.provider_name === selectedProvider
            );
            
            providerModels.forEach(apiInfo => {
                const option = document.createElement('option');
                option.value = apiInfo.model_name;
                option.textContent = apiInfo.display_name || apiInfo.model_name;
                option.dataset.apiInfoId = apiInfo.id;
                if (apiInfo.is_default) {
                    option.selected = true;
                }
                modelDropdown.appendChild(option);
            });
        }
    }

    async loadMarketPredictionPrompt() {
        try {
            const response = await fetch('/api/playground/prompt');
            const data = await response.json();
            
            if (response.ok) {
                this.currentPrompt = data;
                this.renderPromptInfo(data);
                this.populatePromptEditors(data);
                this.showAlert('info', `Loaded prompt: ${data.name} v${data.version}`);
            } else {
                this.showAlert('error', data.error || 'Failed to load market prediction prompt');
            }
        } catch (error) {
            this.showAlert('error', 'Error loading prompt: ' + error.message);
        }
    }

    renderPromptInfo(promptData) {
        const container = document.getElementById('prompt-info');
        if (!container) return;

        container.innerHTML = `
            <div class="current-settings-grid">
                <div class="setting-item">
                    <div class="setting-label">Prompt Name</div>
                    <div class="setting-value">${promptData.name}</div>
                </div>
                <div class="setting-item">
                    <div class="setting-label">Version</div>
                    <div class="setting-value">v${promptData.version}</div>
                </div>
                <div class="setting-item">
                    <div class="setting-label">Parser Type</div>
                    <div class="setting-value">${promptData.parser_type}</div>
                </div>
                <div class="setting-item">
                    <div class="setting-label">Status</div>
                    <div class="setting-value">${promptData.is_active ? 'Active' : 'Inactive'}</div>
                </div>
            </div>
            ${promptData.description ? `<p style="margin-top: 10px; color: #6b7280; font-style: italic;">${promptData.description}</p>` : ''}
        `;
    }

    populatePromptEditors(promptData) {
        const systemEditor = document.getElementById('system-prompt-editor');
        const userEditor = document.getElementById('user-template-editor');
        
        if (systemEditor) {
            systemEditor.value = promptData.system_prompt || '';
        }
        if (userEditor) {
            userEditor.value = promptData.user_template || '';
        }
    }

    async saveTestPrompt() {
        try {
            const systemPrompt = document.getElementById('system-prompt-editor').value;
            const userTemplate = document.getElementById('user-template-editor').value;
            
            if (!systemPrompt.trim() || !userTemplate.trim()) {
                this.showAlert('error', 'Both system prompt and user template are required');
                return;
            }

            this.showLoading();

            const response = await fetch('/api/playground/save-test-prompt', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    name: this.currentPrompt?.name || 'market_prediction_test',
                    system_prompt: systemPrompt,
                    user_template: userTemplate,
                    parser_type: this.currentPrompt?.parser_type || 'json',
                    short_name: this.currentPrompt?.short_name || 'TEST'
                })
            });

            const result = await response.json();

            if (response.ok) {
                this.savedTestPrompt = result.id;
                this.showAlert('success', `Test prompt saved: ${result.name}`);
            } else {
                this.showAlert('error', result.error || 'Failed to save test prompt');
            }
        } catch (error) {
            this.showAlert('error', 'Error saving test prompt: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }

    async testLLMPrediction(forceUpdate = false) {
        try {
            console.log('Force Update:', forceUpdate);
            this.showLoading();
            
            // Get selected API provider and model
            const apiProvider = document.getElementById('playground-api-provider').value;
            const modelName = document.getElementById('playground-model').value;
            
            if (!apiProvider || !modelName) {
                this.showAlert('error', 'Please select both API provider and model');
                return;
            }
            const system_prompt = document.getElementById('system-prompt-editor').value;
            const user_template = document.getElementById('user-template-editor').value;
            
            // Build URL with parameters
            const url = new URL('/api/llm-prediction', window.location.origin);
            url.searchParams.append('api', apiProvider);
            url.searchParams.append('model', modelName);
            url.searchParams.append('system_prompt', system_prompt);
            url.searchParams.append('user_template', user_template);
            url.searchParams.append('force_update', 'true');

            const response = await fetch(url);
            const data = await response.json();
            const resultContainer = document.getElementById('test-result');
            
            if (response.ok) {
                resultContainer.className = 'test-response success';
                resultContainer.textContent = JSON.stringify(data, null, 2);
                this.showAlert('success', forceUpdate ? 'LLM prediction updated successfully' : 'LLM prediction completed successfully');
            } else {
                resultContainer.className = 'test-response error';
                resultContainer.textContent = data.error || 'Prediction failed';
                this.showAlert('error', data.error || 'LLM prediction failed');
            }
        } catch (error) {
            const resultContainer = document.getElementById('test-result');
            resultContainer.className = 'test-response error';
            resultContainer.textContent = 'Error: ' + error.message;
            this.showAlert('error', 'Error testing LLM: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }

    async forceUpdateAnalysis() {
        try {
            this.showLoading();
            const apiProvider = document.getElementById('api-provider').value;
            
            const response = await fetch(`/api/llm-prediction?api=${apiProvider}&force_update=true`);
            const data = await response.json();
            
            if (response.ok) {
                this.showAlert('success', 'Analysis updated successfully!');
            } else {
                this.showAlert('error', data.error || 'Failed to update analysis');
            }
        } catch (error) {
            this.showAlert('error', 'Error updating analysis: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }

    async sendEmail() {
        try {
            const emailType = document.getElementById('email-type').value;
            const recipientFilter = document.getElementById('recipient-filter').value;
            
            let userIds = [];
            
            if (recipientFilter === 'selected') {
                userIds = Array.from(this.selectedUsers);
                if (userIds.length === 0) {
                    this.showAlert('error', 'Please select at least one user');
                    return;
                }
            }
            
            this.showLoading();
            
            const response = await fetch('/api/send-email', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    email_type: emailType,
                    recipient_filter: recipientFilter,
                    user_ids: userIds
                })
            });
            
            const data = await response.json();
            
            if (response.ok) {
                this.showAlert('success', `Email sent successfully! ${data.message || ''}`);
            } else {
                this.showAlert('error', data.error || 'Failed to send email');
            }
        } catch (error) {
            this.showAlert('error', 'Error sending email: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }

    async loadUsers() {
        try {
            this.showLoading();
            
            const response = await fetch('/api/admin/users');
            const data = await response.json();
            
            if (response.ok) {
                this.users = data.users || [];
                this.renderUsers();
                this.showAlert('info', `Loaded ${this.users.length} users`);
            } else {
                this.showAlert('error', data.error || 'Failed to load users');
            }
        } catch (error) {
            this.showAlert('error', 'Error loading users: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }

    renderUsers() {
        const tbody = document.getElementById('users-table-body');
        if (!tbody) return;
        
        tbody.innerHTML = '';

        this.users.forEach(user => {
            const row = document.createElement('tr');
            
            const statusClass = user.is_active ? 'status-active' : 'status-inactive';
            const statusText = user.is_active ? 'Active' : 'Inactive';
            const adminBadge = user.is_admin ? '<span class="admin-badge">ADMIN</span>' : '';
            const verifiedBadge = user.is_verified ? 
                '<span class="verified-badge">VERIFIED</span>' : 
                '<span class="unverified-badge">UNVERIFIED</span>';
            const createdDate = new Date(user.created_at).toLocaleDateString();
            const lastLogin = user.last_login ? new Date(user.last_login).toLocaleDateString() : 'Never';
            const verifyButton = !user.is_verified ? 
                `<button class="btn-verify" onclick="window.adminDashboard.verifyUser(${user.id})">Verify</button>` : '';
            
            row.innerHTML = `
                <td>
                    <input type="checkbox" class="user-checkbox" value="${user.id}" 
                           ${this.selectedUsers.has(user.id) ? 'checked' : ''}>
                </td>
                <td>${user.username}</td>
                <td>${user.email}</td>
                <td>${user.first_name || ''} ${user.last_name || ''}</td>
                <td>
                    <span class="status-indicator ${statusClass}"></span>
                    ${statusText}
                </td>
                <td>${verifiedBadge}</td>
                <td>${adminBadge}</td>
                <td>${createdDate}</td>
                <td>${lastLogin}</td>
                <td>${verifyButton}</td>
            `;
            
            // Add event listener to checkbox
            const checkbox = row.querySelector('.user-checkbox');
            checkbox.addEventListener('change', (e) => {
                if (e.target.checked) {
                    this.selectedUsers.add(parseInt(user.id));
                } else {
                    this.selectedUsers.delete(parseInt(user.id));
                }
                this.updateMasterCheckbox();
            });
            
            tbody.appendChild(row);
        });
    }

    selectAllUsers() {
        this.selectedUsers.clear();
        this.users.forEach(user => {
            this.selectedUsers.add(parseInt(user.id));
        });
        this.updateCheckboxes();
    }

    deselectAllUsers() {
        this.selectedUsers.clear();
        this.updateCheckboxes();
    }

    updateCheckboxes() {
        document.querySelectorAll('.user-checkbox').forEach(checkbox => {
            if (checkbox.id !== 'select-all-checkbox') {
                checkbox.checked = this.selectedUsers.has(parseInt(checkbox.value));
            }
        });
        this.updateMasterCheckbox();
    }

    updateMasterCheckbox() {
        const masterCheckbox = document.getElementById('select-all-checkbox');
        if (!masterCheckbox) return;
        
        const totalUsers = this.users.length;
        const selectedCount = this.selectedUsers.size;
        
        if (selectedCount === 0) {
            masterCheckbox.checked = false;
            masterCheckbox.indeterminate = false;
        } else if (selectedCount === totalUsers) {
            masterCheckbox.checked = true;
            masterCheckbox.indeterminate = false;
        } else {
            masterCheckbox.checked = false;
            masterCheckbox.indeterminate = true;
        }
    }

    async verifyUser(userId) {
        try {
            this.showLoading();

            const response = await fetch('/api/admin/verify-user', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    user_id: userId
                })
            });

            const data = await response.json();

            if (response.ok) {
                this.showAlert('success', `User verified successfully!`);
                // Refresh the user list to show updated status
                this.loadUsers();
            } else {
                this.showAlert('error', data.error || 'Failed to verify user');
            }
        } catch (error) {
            this.showAlert('error', 'Error verifying user: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }

    // Prompt Management Methods
    async loadPrompts() {
        try {
            this.showLoading();
            
            const response = await fetch('/api/admin/prompts');
            const data = await response.json();
            
            if (response.ok) {
                this.prompts = data.templates || [];
                this.renderPrompts();
                this.showAlert('info', `Loaded ${this.prompts.length} prompt templates`);
            } else {
                this.showAlert('error', data.error || 'Failed to load prompt templates');
            }
        } catch (error) {
            this.showAlert('error', 'Error loading prompt templates: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }

    renderPrompts() {
        const tbody = document.getElementById('prompts-table-body');
        if (!tbody) return;
        
        tbody.innerHTML = '';

        this.prompts.forEach(prompt => {
            const row = document.createElement('tr');
            
            const activeIcon = prompt.is_active ? '<i class="bi bi-check-circle" style="color: #10b981;"></i>' : '<i class="bi bi-x-circle" style="color: #ef4444;"></i>';
            const prodIcon = prompt.is_prod ? '<i class="bi bi-star-fill" style="color: #f59e0b;"></i>' : '<i class="bi bi-star" style="color: #d1d5db;"></i>';
            const createdDate = new Date(prompt.created_at).toLocaleDateString();
            const successRate = prompt.success_rate ? `${prompt.success_rate.toFixed(1)}%` : 'N/A';
            
            row.innerHTML = `
                <td><strong>${prompt.name}</strong></td>
                <td>v${prompt.version}</td>
                <td>${activeIcon}</td>
                <td>${prodIcon}</td>
                <td><span style="background: #e5e7eb; padding: 2px 6px; border-radius: 4px; font-size: 0.75rem;">${prompt.parser_type}</span></td>
                <td>${prompt.usage_count || 0}</td>
                <td>${successRate}</td>
                <td>${createdDate}</td>
                <td>
                    <div style="display: flex; gap: 5px;">
                        <button class="btn-verify" onclick="window.adminDashboard.editPrompt('${prompt.id}')" title="Edit">
                            <i class="bi bi-pencil"></i>
                        </button>
                        ${!prompt.is_active ? `<button class="btn-verify" onclick="window.adminDashboard.activatePrompt('${prompt.id}')" title="Activate">
                            <i class="bi bi-check-circle"></i>
                        </button>` : ''}
                        ${!prompt.is_prod ? `<button class="btn-verify" onclick="window.adminDashboard.setProdPrompt('${prompt.id}')" title="Set as Production" style="background: #f59e0b;">
                            <i class="bi bi-star"></i>
                        </button>` : ''}
                        <button class="btn-verify" onclick="window.adminDashboard.clonePrompt('${prompt.id}')" title="Clone">
                            <i class="bi bi-files"></i>
                        </button>
                        <button class="btn-verify" style="background: #ef4444;" onclick="window.adminDashboard.deletePrompt('${prompt.id}')" title="Delete">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            `;
            
            tbody.appendChild(row);
        });
    }

    filterPrompts(query) {
        const tbody = document.getElementById('prompts-table-body');
        if (!tbody) return;
        
        const rows = tbody.querySelectorAll('tr');
        
        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            const shouldShow = text.includes(query.toLowerCase());
            row.style.display = shouldShow ? '' : 'none';
        });
    }

    showCreatePromptModal() {
        const name = prompt('Enter prompt name:');
        if (!name) return;
        
        const systemPrompt = prompt('Enter system prompt:');
        if (!systemPrompt) return;
        
        const userTemplate = prompt('Enter user template:');
        if (!userTemplate) return;
        
        const parserType = prompt('Enter parser type (json/regex/custom):', 'json');
        if (!parserType) return;
        
        const description = prompt('Enter description (optional):') || '';
        
        this.createPrompt({
            name,
            system_prompt: systemPrompt,
            user_template: userTemplate,
            parser_type: parserType,
            description
        });
    }

    async createPrompt(data) {
        try {
            this.showLoading();
            
            const response = await fetch('/api/admin/prompts', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });
            
            const result = await response.json();
            
            if (response.ok) {
                this.showAlert('success', 'Prompt template created successfully!');
                this.loadPrompts();
            } else {
                this.showAlert('error', result.error || 'Failed to create prompt template');
            }
        } catch (error) {
            this.showAlert('error', 'Error creating prompt template: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }

    async editPrompt(promptId) {
        try {
            // Get current prompt data
            const response = await fetch(`/api/admin/prompts/${promptId}`);
            const data = await response.json();
            
            if (!response.ok) {
                this.showAlert('error', data.error || 'Failed to load prompt template');
                return;
            }
            
            const template = data.template;
            
            // Simple edit dialog (in a real app, you'd use a proper modal)
            const newDescription = prompt('Edit description:', template.description || '');
            if (newDescription === null) return;
            
            const isActive = confirm('Should this prompt be active?');
            
            await this.updatePrompt(promptId, {
                description: newDescription,
                is_active: isActive
            });
            
        } catch (error) {
            this.showAlert('error', 'Error editing prompt: ' + error.message);
        }
    }

    async updatePrompt(promptId, data) {
        try {
            this.showLoading();
            
            const response = await fetch(`/api/admin/prompts/${promptId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });
            
            const result = await response.json();
            
            if (response.ok) {
                this.showAlert('success', 'Prompt template updated successfully!');
                this.loadPrompts();
            } else {
                this.showAlert('error', result.error || 'Failed to update prompt template');
            }
        } catch (error) {
            this.showAlert('error', 'Error updating prompt template: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }

    async activatePrompt(promptId) {
        try {
            this.showLoading();
            
            const response = await fetch(`/api/admin/prompts/${promptId}/activate`, {
                method: 'POST'
            });
            
            const result = await response.json();
            
            if (response.ok) {
                this.showAlert('success', 'Prompt template activated successfully!');
                this.loadPrompts();
            } else {
                this.showAlert('error', result.error || 'Failed to activate prompt template');
            }
        } catch (error) {
            this.showAlert('error', 'Error activating prompt template: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }

    async clonePrompt(promptId) {
        const newName = prompt('Enter name for cloned prompt:');
        if (!newName) return;
        
        try {
            this.showLoading();
            
            const response = await fetch(`/api/admin/prompts/${promptId}/clone`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ new_name: newName })
            });
            
            const result = await response.json();
            
            if (response.ok) {
                this.showAlert('success', 'Prompt template cloned successfully!');
                this.loadPrompts();
            } else {
                this.showAlert('error', result.error || 'Failed to clone prompt template');
            }
        } catch (error) {
            this.showAlert('error', 'Error cloning prompt template: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }

    async deletePrompt(promptId) {
        if (!confirm('Are you sure you want to delete this prompt template?')) return;
        
        try {
            this.showLoading();
            
            const response = await fetch(`/api/admin/prompts/${promptId}`, {
                method: 'DELETE'
            });
            
            const result = await response.json();
            
            if (response.ok) {
                this.showAlert('success', 'Prompt template deleted successfully!');
                this.loadPrompts();
            } else {
                this.showAlert('error', result.error || 'Failed to delete prompt template');
            }
        } catch (error) {
            this.showAlert('error', 'Error deleting prompt template: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }

    async setProdPrompt(promptId) {
        if (!confirm('Are you sure you want to set this prompt as the production version? This will unset the current production prompt.')) return;
        
        try {
            this.showLoading();
            
            const response = await fetch(`/api/admin/prompts/${promptId}/set-prod`, {
                method: 'POST'
            });
            
            const result = await response.json();
            
            if (response.ok) {
                this.showAlert('success', 'Prompt template set as production successfully!');
                this.loadPrompts();
            } else {
                this.showAlert('error', result.error || 'Failed to set prompt template as production');
            }
        } catch (error) {
            this.showAlert('error', 'Error setting prompt template as production: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }

    async loadApiProviders() {
        try {
            this.showLoading();

            const response = await fetch('/api/admin/api-providers');
            const data = await response.json();

            if (response.ok) {
                this.apiProviders = data.providers;
                this.defaultApiInfo = data.default_api_info;
                this.renderApiProviders();
                this.updateApiProviderDropdown();
                this.showAlert('info', `Loaded ${Object.keys(this.apiProviders).length} API providers`);
            } else {
                this.showAlert('error', data.error || 'Failed to load API providers');
            }
        } catch (error) {
            this.showAlert('error', 'Error loading API providers: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }

    renderApiProviders() {
        const tbody = document.getElementById('api-providers-table-body');
        if (!tbody) return;
        
        tbody.innerHTML = '';

        for (const [providerName, modelInfos] of Object.entries(this.apiProviders)) {

            modelInfos.forEach(modelInfo => {
                const row = document.createElement('tr');
                row.innerHTML = '<td>' + providerName + '</td>';
                row.innerHTML += `
                    <td>${modelInfo.model_name}</td>
                    <td>${modelInfo.display_name || ''}</td>
                    <td>
                        <span class="status-indicator ${modelInfo.is_active ? 'status-active' : 'status-inactive'}"></span>
                        ${modelInfo.is_active ? 'Active' : 'Inactive'}
                    </td>
                    <td>${modelInfo.is_default ? 'Default' : ''}</td>
                    <td>${modelInfo.description || ''}</td>
                    <td>
                        <button class="btn-verify" onclick="window.adminDashboard.setDefaultApiProvider(${modelInfo.id})">Set Default</button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }
    }

    updateApiProviderDropdown() {
        const dropdown = document.getElementById('api-provider');
        if (!dropdown) return;
        
        dropdown.innerHTML = '';

        // Add default option
        const defaultOption = document.createElement('option');
        defaultOption.value = '';
        defaultOption.textContent = 'Select API Provider';
        dropdown.appendChild(defaultOption);

        // Add options grouped by provider
        Object.keys(this.apiProviders).forEach(providerName => {
            const option = document.createElement('option');
            option.value = providerName;
            option.textContent = providerName.charAt(0).toUpperCase() + providerName.slice(1);
            if (this.defaultApiInfo && this.defaultApiInfo.provider_name === providerName) {
                option.selected = true;
            }
            dropdown.appendChild(option);
        });
    }

    async addApiProvider() {
        try {
            const providerName = document.getElementById('provider-name').value;
            const modelName = document.getElementById('model-name').value;
            const displayName = document.getElementById('display-name').value;
            const description = document.getElementById('description').value;
            const isActive = document.getElementById('is-active').checked;

            if (!providerName || !modelName) {
                this.showAlert('error', 'Provider name and model name are required');
                return;
            }

            this.showLoading();

            const response = await fetch('/api/admin/api-providers', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    provider_name: providerName,
                    model_name: modelName,
                    display_name: displayName,
                    description: description,
                    is_active: isActive
                })
            });

            const data = await response.json();

            if (response.ok) {
                this.showAlert('success', 'API provider/model added successfully!');
                // Clear form
                document.getElementById('provider-name').value = '';
                document.getElementById('model-name').value = '';
                document.getElementById('display-name').value = '';
                document.getElementById('description').value = '';
                document.getElementById('is-active').checked = true;
                // Refresh the list
                this.loadApiProviders();
            } else {
                this.showAlert('error', data.error || 'Failed to add API provider/model');
            }
        } catch (error) {
            this.showAlert('error', 'Error adding API provider/model: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }

    async setDefaultApiProvider(apiProviderId) {
        try {
            this.showLoading();

            const response = await fetch(`/api/admin/api-providers/${apiProviderId}/set-default`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            const data = await response.json();

            if (response.ok) {
                this.showAlert('success', 'Default API provider/model set successfully!');
                // Refresh the list
                this.loadApiProviders();
            } else {
                this.showAlert('error', data.error || 'Failed to set default API provider/model');
            }
        } catch (error) {
            this.showAlert('error', 'Error setting default API provider/model: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }

    async deleteApiProvider(apiProviderId) {
        if (!confirm('Are you sure you want to delete this API provider/model configuration?')) {
            return;
        }

        try {
            this.showLoading();

            const response = await fetch(`/api/admin/api-providers/${apiProviderId}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            const data = await response.json();

            if (response.ok) {
                this.showAlert('success', 'API provider/model deleted successfully!');
                // Refresh the list
                this.loadApiProviders();
            } else {
                this.showAlert('error', data.error || 'Failed to delete API provider/model');
            }
        } catch (error) {
            this.showAlert('error', 'Error deleting API provider/model: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }
}

// Initialize admin dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.adminDashboard = new AdminDashboard();
});