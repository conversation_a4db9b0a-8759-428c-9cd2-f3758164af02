/**
 * Market Analysis JavaScript Module
 * Handles interactive charts and AI analysis display with real-time updates
 *
 * <AUTHOR> Team
 * @version 2.0.0
 */

// Configuration Constants
const CONFIG = {
    CHART_COLORS: {
        PRIMARY: '#007bff',
        VOLUME: 'rgba(255,165,0,0.6)',
        UP: '#10b981',
        DOWN: '#ef4444',
        NEUTRAL: '#6b7280',
        SUCCESS: '#10b981',
        WARNING: '#f59e0b',
        BLUE: '#3b82f6'
    },

    STYLE_CONSTANTS: {
        BORDER_RADIUS: '12px',
        CARD_PADDING: '20px',
        SECTION_PADDING: '24px',
        GRADIENT_BLUE: 'linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%)',
        GRADIENT_GRAY: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',
        GRADIENT_GREEN: 'linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%)'
    },

    DOM_IDS: {
        TICKER_SELECT: 'ticker-selector',
        PRICE_CHART: 'price-chart',
        AI_ANALYSIS_CONTENT: 'ai-analysis-content'
    },

    UPDATE_INTERVALS: {
        MARKET_OPEN: 2 * 60 * 1000,      // 2 minutes during market hours
        MARKET_CLOSED: 30 * 60 * 1000,   // 30 minutes when market is closed
        STATUS_CHECK: 60 * 1000          // 1 minute for status checks
    },

    MARKET_HOURS: {
        OPEN: { hour: 9, minute: 30 },   // 9:30 AM ET
        CLOSE: { hour: 16, minute: 0 }   // 4:00 PM ET
    }
};

import { capitalizeFirst, escapeHtml } from './utils.js';

/**
 * HTML Template Generator Class
 * Handles all HTML template generation for market analysis components
 */
class AnalysisTemplateGenerator {
    generateLoadingSpinner(message, colorClass = 'text-primary') {
        return `
            <div class="loading-spinner" style="text-align: center; padding: 40px; min-height: 200px; display: flex; flex-direction: column; justify-content: center; align-items: center;">
                <div class="spinner-border ${colorClass}" role="status" style="margin-bottom: 15px;">
                    <span class="visually-hidden">${message}</span>
                </div>
                <p class="mt-2" style="color: #6c757d; font-size: 1rem;">${message}</p>
                <small style="color: #adb5bd; margin-top: 5px;">Please wait...</small>
            </div>
        `;
    }

    generateErrorMessage(message) {
        return `
            <div class="error-message" style="text-align: center; padding: 40px; background: #fee2e2; border: 1px solid #fecaca; border-radius: 8px; color: #991b1b;">
                <i class="bi bi-exclamation-triangle" style="font-size: 2rem; margin-bottom: 10px; display: block;"></i>
                <strong>Error:</strong> ${escapeHtml(message)}
            </div>
        `;
    }

    generateMarketOverview(data) {
        if (!this.hasNumericData(data)) return '';
        
        return `
            <div style="background: ${STYLE_CONSTANTS.GRADIENT_BLUE}; border: 1px solid #93c5fd; padding: ${STYLE_CONSTANTS.SECTION_PADDING}; margin: 24px 0; border-radius: 16px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);">
                <h6 style="color: #1e40af; font-weight: 700; margin-bottom: 20px; font-size: 18px; display: flex; align-items: center; gap: 10px;">
                    <div style="background: linear-gradient(135deg, ${CHART_COLORS.BLUE}, #1d4ed8); color: white; padding: 8px; border-radius: 10px; display: flex; align-items: center; justify-content: center;">
                        <i class="bi bi-graph-up-arrow" style="font-size: 16px;"></i>
                    </div>
                    Market Overview
                </h6>
                
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 20px;">
                    ${this.generatePredictionCard(data)}
                    ${this.generateCriticalLevels(data)}
                </div>
            </div>
        `;
    }

    generatePredictionCard(data) {
        if (!data.prediction || !data.confidence) return '';
        
        const confidencePercent = (data.confidence * 100).toFixed(1);
        const predictionClass = data.prediction === 'up' ? CHART_COLORS.UP : 
                              data.prediction === 'down' ? CHART_COLORS.DOWN : CHART_COLORS.NEUTRAL;
        const predictionIcon = data.prediction === 'up' ? 'up' : data.prediction === 'down' ? 'down' : 'right';
        
        return `
            <div style="background: rgba(255, 255, 255, 0.9); border-radius: ${STYLE_CONSTANTS.BORDER_RADIUS}; padding: ${STYLE_CONSTANTS.CARD_PADDING}; border: 1px solid #bfdbfe;">
                <h6 style="color: #1e40af; font-weight: 600; margin-bottom: 15px; font-size: 14px;">MARKET PREDICTION</h6>
                <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 15px;">
                    <i class="bi bi-arrow-${predictionIcon}" style="color: ${predictionClass}; font-size: 28px;"></i>
                    <div>
                        <div style="color: ${predictionClass}; font-weight: 700; font-size: 24px; line-height: 1;">
                            ${data.prediction.toUpperCase()}
                        </div>
                        <div style="color: #64748b; font-size: 12px; font-weight: 500;">
                            ${confidencePercent}% Confidence
                        </div>
                    </div>
                </div>
                <div style="background: #f1f5f9; border-radius: 8px; height: 8px; overflow: hidden;">
                    <div style="background: ${predictionClass}; height: 100%; width: ${confidencePercent}%; transition: width 0.3s ease;"></div>
                </div>
            </div>
        `;
    }

    generateCriticalLevels(data) {
        if (!data.critical_levels || Object.keys(data.critical_levels).length === 0) return '';
        
        const levelsHtml = Object.entries(data.critical_levels).map(([key, value]) => {
            if (typeof value === 'number') {
                return `
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px 0; border-bottom: 1px solid #f1f5f9;">
                        <span style="color: #475569; font-weight: 500; font-size: 13px;">${capitalizeFirst(key)}</span>
                        <span style="color: #1e293b; font-weight: 700; font-size: 14px;">$${value.toFixed(2)}</span>
                    </div>
                `;
            } else if (typeof value === 'string') {
                return `
                    <div style="padding: 8px 0; border-bottom: 1px solid #f1f5f9;">
                        <div style="color: #475569; font-weight: 500; font-size: 13px; margin-bottom: 4px;">${capitalizeFirst(key)}</div>
                        <div style="color: #1e293b; font-weight: 600; font-size: 13px;">${escapeHtml(value)}</div>
                    </div>
                `;
            }
            return '';
        }).join('');
        
        return `
            <div style="background: rgba(255, 255, 255, 0.9); border-radius: ${STYLE_CONSTANTS.BORDER_RADIUS}; padding: ${STYLE_CONSTANTS.CARD_PADDING}; border: 1px solid #bfdbfe;">
                <h6 style="color: #1e40af; font-weight: 600; margin-bottom: 15px; font-size: 14px;">CRITICAL LEVELS</h6>
                <div style="display: grid; gap: 12px;">
                    ${levelsHtml}
                </div>
            </div>
        `;
    }

    generateTriggerDisplay(triggerText) {
        return `
            <div class="trigger-display">
                <div class="trigger-header">
                    <div class="trigger-icon">
                        <i class="bi bi-exclamation-diamond"></i>
                    </div>
                    <span class="trigger-label">TRIGGER</span>
                </div>
                <div class="trigger-content">
                    <div class="trigger-text">
                        ${escapeHtml(triggerText)}
                    </div>
                    <div class="trigger-indicator">
                        <div class="trigger-pulse"></div>
                        <span class="trigger-status">ACTIVE</span>
                    </div>
                </div>
            </div>
        `;
    }

    hasNumericData(data) {
        return (data.prediction && data.confidence) || 
               (data.critical_levels && Object.keys(data.critical_levels).length > 0);
    }
}

/**
 * API Service Class
 * Handles API communication for analysis data
 */
class MarketAnalysisAPI {
    async fetchAnalysis(apiProvider, forceUpdate = false) {
        const params = new URLSearchParams({ api: apiProvider });
        if (forceUpdate) {
            params.append('force_update', 'true');
        }
        
        const response = await fetch(`/api/llm-prediction?${params.toString()}`);
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.error || 'Failed to load AI analysis');
        }
        
        return data;
    }
}

class MarketAnalysis {
    constructor() {
        this.currentTicker = 'SPY';
        this.currentApiProvider = 'gemini';
        this.analysisData = null;
        this.currentOutlookData = null;
        this.currentOutlookId = null;
        this.isAnalysisLoading = false;
        this.priceGraph = null;

        // Initialize helper classes
        this.templateGenerator = new AnalysisTemplateGenerator();
        this.apiService = new MarketAnalysisAPI();

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadInitialData();
        this.initializePriceGraph();
    }

    setupEventListeners() {
        // Ticker selection
        const tickerSelect = document.getElementById(CONFIG.DOM_IDS.TICKER_SELECT);
        if (tickerSelect) {
            tickerSelect.addEventListener('change', (e) => {
                this.currentTicker = e.target.value;
                if (this.priceGraph) {
                    this.priceGraph.setTicker(this.currentTicker);
                }
            });
        }

    }

    async loadInitialData() {
        await this.loadAnalysis();
    }

    initializePriceGraph() {
        if (window.PriceGraph) {
            this.priceGraph = new PriceGraph(this.currentTicker);
        } else {
            console.warn('PriceGraph class not available. Make sure price-graph.js is loaded.');
        }
    }


    async loadAnalysis() {
        if (this.isAnalysisLoading) {
            console.log('Analysis already loading, skipping...');
            return;
        }
        
        this.isAnalysisLoading = true;
        
        try {
            this.showAnalysisLoading();
            
            const data = await this.apiService.fetchAnalysis(this.currentApiProvider);
            this.analysisData = data;
            this.renderAnalysis(data);

        } catch (error) {
            console.error('Error loading AI analysis:', error);
            this.showAnalysisError(error.message);
        } finally {
            this.isAnalysisLoading = false;
        }
    }



    showAnalysisLoading() {
        const analysisContainer = document.getElementById(CONFIG.DOM_IDS.AI_ANALYSIS_CONTENT);
        if (analysisContainer) {
            analysisContainer.innerHTML = this.templateGenerator.generateLoadingSpinner(
                `Generating AI market analysis...`,
                'text-success'
            );
        }
    }

    showAnalysisError(message) {
        const analysisContainer = document.getElementById(CONFIG.DOM_IDS.AI_ANALYSIS_CONTENT);
        if (analysisContainer) {
            analysisContainer.innerHTML = this.templateGenerator.generateErrorMessage(
                `Error loading AI analysis: ${message}`
            );
        }
    }



    renderAnalysis(data) {
        const analysisContainer = document.getElementById('ai-analysis-content');
        if (!analysisContainer) return;

        let html = '';

        // Compact Market Overview Section - Merged prediction and levels
        let hasNumericData = (data.prediction && data.confidence) || (data.critical_levels && Object.keys(data.critical_levels).length > 0);

        if (hasNumericData) {
            html += `
                <div class="market-overview-section">
                    <div class="market-overview-card">
                        <h6 class="market-overview-title">
                            <i class="bi bi-graph-up" style="color:rgb(1, 5, 20); margin-right: 8px;"></i>
                            MARKET OVERVIEW
                        </h6>
                        <div class="market-overview-content">
            `;

            // Market Direction Prediction (left side)
            if (data.prediction && data.confidence) {
                const confidencePercent = (data.confidence * 100).toFixed(1);
                const predictionClass = data.prediction === 'up' ? '#10b981' :
                                      data.prediction === 'down' ? '#ef4444' : '#6b7280';
                const predictionIcon = data.prediction === 'up' ? 'up' : data.prediction === 'down' ? 'down' : 'right';

                html += `
                    <div class="prediction-section">
                        <div class="prediction-header">
                            <span class="prediction-label">DIRECTION</span>
                            <i class="bi bi-arrow-${predictionIcon}" style="color: ${predictionClass}; font-size: 18px;"></i>
                        </div>
                        <div class="prediction-value" style="color: ${predictionClass};">
                            ${data.prediction.toUpperCase()}
                        </div>
                        <div class="prediction-confidence">
                            ${confidencePercent}% confidence
                        </div>
                        <div class="prediction-bar">
                            <div class="prediction-bar-fill" style="background: ${predictionClass}; width: ${confidencePercent}%;"></div>
                        </div>
                    </div>
                `;
            }

            // Critical Levels (right side)
            if (data.critical_levels && Object.keys(data.critical_levels).length > 0) {
                html += `
                    <div class="levels-section">
                        <div class="levels-header">
                            <i class="bi bi-bullseye" style="color: #1e40af; font-size: 16px;"></i>
                            <span class="levels-label">KEY LEVELS</span>
                        </div>
                        <div class="levels-grid">
                `;

                Object.entries(data.critical_levels).forEach(([key, value]) => {
                    if (typeof value === 'number') {
                        const levelType = key.toLowerCase().includes('support') ? 'support' :
                                        key.toLowerCase().includes('resistance') ? 'resistance' : 'neutral';
                        const levelColor = levelType === 'support' ? '#10b981' :
                                         levelType === 'resistance' ? '#ef4444' : '#6b7280';
                        const levelIcon = levelType === 'support' ? 'arrow-down' :
                                        levelType === 'resistance' ? 'arrow-up' : 'dash';

                        html += `
                            <div class="level-item">
                                <div class="level-info">
                                    <i class="bi bi-${levelIcon}" style="color: ${levelColor}; font-size: 11px;"></i>
                                    <span class="level-name">${key}</span>
                                </div>
                                <span class="level-value" style="color: ${levelColor};">$${value.toFixed(2)}</span>
                            </div>                                            
                        `;
                    }
                });

                html += `
                        </div>
                    </div>
                `;
            }

            html += `
                        ${data.critical_levels.trigger ? this.templateGenerator.generateTriggerDisplay(data.critical_levels.trigger) : ''}
                        </div>
                    </div>
                </div>
            `;
        }

        // Key Evidence - Modern UI Design
        if (data.key_evidence && data.key_evidence.length > 0) {            
            html += `
                <div style="background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%); border: 1px solid #cbd5e1; padding: 24px; margin: 24px 0; border-radius: 16px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);">
                    <h6 style="color: #1e293b; font-weight: 700; margin-bottom: 20px; font-size: 18px; display: flex; align-items: center; gap: 8px;">
                        <i class="bi bi-lightbulb-fill" style="color: #3b82f6;"></i>
                        Key Evidence
                        <span style="background: #3b82f6; color: white; font-size: 12px; padding: 2px 8px; border-radius: 12px; font-weight: 600;">${data.key_evidence.length}</span>
                    </h6>
                    <div style="display: grid; gap: 12px;">
            `;

            data.key_evidence.forEach((evidence) => {
                let evidenceText = evidence.fact || 'No evidence text available';
                
                // Clean up the evidence text by removing [Article X, Y, Z] references
                evidenceText = evidenceText.replace(/\s*\[Article\s+[\d,\s]+\]\s*\.?\s*$/i, '');
                
                // Check if we have a URL to make it clickable
                const hasUrl = evidence.url && evidence.url !== 'N/A';
                const isMarketData = evidence.article_ref === "MARKET" || evidence.source === "N/A";
                
                html += `
                    <div style="background: #ffffff; border: 1px solid #e2e8f0; border-radius: 12px; padding: 16px; transition: all 0.2s ease; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); position: relative; overflow: visible; min-height: auto;">
                        <div style="position: absolute; top: 0; left: 0; width: 4px; height: 100%; background: ${isMarketData ? '#10b981' : '#3b82f6'};"></div>
                        <div style="margin-left: 12px; word-wrap: break-word; overflow-wrap: break-word;">
                `;
                
                if (hasUrl && !isMarketData) {
                    // Make the entire evidence text clickable
                    html += `
                        <a href="${escapeHtml(evidence.url)}" target="_blank" rel="noopener noreferrer" style="text-decoration: none; color: inherit; display: block;">
                            <div style="color: #1e293b; font-weight: 500; font-size: 14px; line-height: 1.5; margin: 0; padding: 0; cursor: pointer; transition: color 0.2s ease; word-wrap: break-word; overflow-wrap: break-word; hyphens: auto;" onmouseover="this.style.color='#3b82f6'" onmouseout="this.style.color='#1e293b'">
                                ${escapeHtml(evidenceText)}
                            </div>
                            <div style="margin-top: 8px; display: flex; align-items: center; gap: 6px; font-size: 12px; color: #64748b;">
                                <i class="bi bi-box-arrow-up-right" style="font-size: 10px;"></i>
                                <span>Click to read full article</span>
                            </div>
                        </a>
                    `;
                } else {
                    // Non-clickable for market data
                    html += `
                        <div style="color: #1e293b; font-weight: 500; font-size: 14px; line-height: 1.5; margin: 0; padding: 0; word-wrap: break-word; overflow-wrap: break-word; hyphens: auto;">
                            ${escapeHtml(evidenceText)}
                        </div>
                        <div style="margin-top: 8px; display: flex; align-items: center; gap: 6px; font-size: 12px; color: #64748b;">
                            <i class="bi bi-graph-up" style="font-size: 10px;"></i>
                            <span>Market data analysis</span>
                        </div>
                    `;
                }
                
                html += `
                        </div>
                    </div>
                `;
            });

            html += `
                    </div>
                </div>
            `;
        }

        // Dominant Market Theme - Harmonious Design
        if (data.dominant_theme && data.dominant_theme.theme) {
            html += `
                <div style="background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 50%, #e2e8f0 100%); border: 1px solid #cbd5e1; padding: 24px; margin: 24px 0; border-radius: 16px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); position: relative; overflow: hidden;">
                    <div style="position: absolute; top: -50px; right: -50px; width: 100px; height: 100px; background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(147, 197, 253, 0.1)); border-radius: 50%; opacity: 0.6;"></div>
                    <div style="position: absolute; bottom: -30px; left: -30px; width: 60px; height: 60px; background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(167, 243, 208, 0.1)); border-radius: 50%; opacity: 0.4;"></div>
                    
                    <h6 style="color: #475569; font-weight: 700; margin-bottom: 16px; font-size: 18px; display: flex; align-items: center; gap: 10px; position: relative; z-index: 1;">
                        <div style="background: linear-gradient(135deg, #3b82f6, #1d4ed8); color: white; padding: 8px; border-radius: 10px; display: flex; align-items: center; justify-content: center; box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);">
                            <i class="bi bi-lightbulb" style="font-size: 16px;"></i>
                        </div>
                        Dominant Market Theme
                    </h6>
                    
                    <div style="background: rgba(255, 255, 255, 0.95); border-radius: 12px; padding: 20px; margin-bottom: 16px; position: relative; z-index: 1; border-left: 4px solid #3b82f6; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);">
                        <p style="color: #334155; font-weight: 500; line-height: 1.7; margin: 0; font-size: 16px;">
                            ${escapeHtml(data.dominant_theme.theme)}
                        </p>
                    </div>
            `;

            // Add supporting articles if available
            if (data.dominant_theme.supporting_articles && data.dominant_theme.supporting_articles.length > 0) {
                html += `
                    <div style="position: relative; z-index: 1;">
                        <h6 style="color: #475569; font-weight: 600; font-size: 14px; margin-bottom: 12px; display: flex; align-items: center; gap: 8px;">
                            <i class="bi bi-collection" style="font-size: 14px;"></i>
                            Supporting Articles
                            <span style="background: rgba(59, 130, 246, 0.1); color: #3b82f6; font-size: 11px; padding: 2px 6px; border-radius: 8px; font-weight: 700;">${data.dominant_theme.supporting_articles.length}</span>
                        </h6>
                        <div style="display: grid; gap: 8px;">
                `;

                data.dominant_theme.supporting_articles.forEach(article => {
                    const hasUrl = article.url && article.url !== 'N/A';
                    const articleTitle = article.article_title || 'Article';
                    const articleSource = article.source || 'Unknown Source';
                    
                    html += `
                        <div style="background: rgba(255, 255, 255, 0.9); border-radius: 8px; padding: 12px; border: 1px solid rgba(203, 213, 225, 0.5); transition: all 0.2s ease; hover: box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);">
                    `;
                    
                    if (hasUrl) {
                        html += `
                            <a href="${escapeHtml(article.url)}" target="_blank" rel="noopener noreferrer" style="text-decoration: none; color: inherit; display: block;">
                                <div style="display: flex; align-items: start; gap: 10px;">
                                    <div style="background: linear-gradient(135deg, #3b82f6, #1d4ed8); color: white; padding: 6px; border-radius: 6px; flex-shrink: 0; margin-top: 2px;">
                                        <i class="bi bi-newspaper" style="font-size: 12px;"></i>
                                    </div>
                                    <div style="flex: 1; min-width: 0;">
                                        <div style="color: #334155; font-weight: 600; font-size: 13px; line-height: 1.4; margin-bottom: 4px; cursor: pointer; transition: color 0.2s ease;" onmouseover="this.style.color='#3b82f6'" onmouseout="this.style.color='#334155'">
                                            ${escapeHtml(articleTitle)}
                                        </div>
                                        <div style="color: #64748b; font-size: 11px; font-weight: 500; display: flex; align-items: center; gap: 4px;">
                                            <span>${escapeHtml(articleSource)}</span>
                                            <i class="bi bi-box-arrow-up-right" style="font-size: 9px;"></i>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        `;
                    } else {
                        html += `
                            <div style="display: flex; align-items: start; gap: 10px;">
                                <div style="background: #64748b; color: white; padding: 6px; border-radius: 6px; flex-shrink: 0; margin-top: 2px;">
                                    <i class="bi bi-newspaper" style="font-size: 12px;"></i>
                                </div>
                                <div style="flex: 1; min-width: 0;">
                                    <div style="color: #334155; font-weight: 600; font-size: 13px; line-height: 1.4; margin-bottom: 4px;">
                                        ${escapeHtml(articleTitle)}
                                    </div>
                                    <div style="color: #64748b; font-size: 11px; font-weight: 500;">
                                        ${escapeHtml(articleSource)}
                                    </div>
                                </div>
                            </div>
                        `;
                    }
                    
                    html += `</div>`;
                });

                html += `
                        </div>
                    </div>
                `;
            }

            html += `</div>`;
        }

        // Market Outlook Section
        if (data.detailed_outlook && Object.keys(data.detailed_outlook).length > 0) {
            // Only use the specified keys: short_term, mid_term, long_term
            const termOrder = ['short_term', 'medium_term', 'long_term'];
            const outlookTerms = termOrder.filter(term => data.detailed_outlook[term]);
            
            if (outlookTerms.length === 0) return; // No valid outlook terms found
            const randomId = Math.random().toString(36).substring(2, 9);
            
            // Store the outlook data for tab switching
            this.currentOutlookData = data.detailed_outlook;
            this.currentOutlookId = randomId;
            
            html += `
                <div class="market-outlook-container">
                    <h6 class="market-outlook-header">
                        <i class="bi bi-clock-history"></i>
                        Market Outlook
                    </h6>
                    
                    <!-- Tab Navigation -->
                    <div class="outlook-tab-nav">
            `;
            
            outlookTerms.forEach((term, index) => {
                const isActive = index === 0;
                const termDisplay = term.replace('_', ' ').split(' ').map(word => 
                    word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
                const timeRange = data.detailed_outlook[term].time_range;
                
                html += `
                    <button onclick="window.marketAnalysis.switchOutlookTab('${term}')" 
                            id="tab_${term}_${randomId}"
                            class="${isActive ? 'active' : ''}">
                        ${termDisplay} (${timeRange})
                    </button>
                `;
            });
            
            html += `
                    </div>
                    
                    <!-- Tab Content -->
                    <div id="outlook_content_${randomId}">
            `;
            
            // Generate content for each tab
            outlookTerms.forEach((term, index) => {
                const outlook = data.detailed_outlook[term];
                if (!outlook) return;
                
                const direction = outlook.direction || 'FLAT';
                const expectedMove = outlook.expected_move_pct || 'Unknown';
                const confidence = outlook.confidence || 'LOW';
                const keyEvidence = outlook.key_evidence || [];
                const isActive = index === 0;
                
                html += `
                    <div id="content_${term}_${randomId}" class="outlook-tab-content ${isActive ? 'active' : ''}">
                        <!-- Metrics Grid -->
                        <div class="outlook-metrics-grid">
                            <!-- Direction -->
                            <div class="outlook-metric-card">
                                <div class="outlook-metric-label">DIRECTION</div>
                                <div class="outlook-direction ${direction.toLowerCase()}">
                                    <i class="bi bi-arrow-${direction === 'UP' ? 'up' : direction === 'DOWN' ? 'down' : 'right'}"></i>
                                    <span>${direction}</span>
                                </div>
                            </div>
                            
                            <!-- Expected Move -->
                            <div class="outlook-metric-card">
                                <div class="outlook-metric-label">EXPECTED MOVE</div>
                                <div class="outlook-expected-move">${escapeHtml(expectedMove)}</div>
                            </div>
                            
                            <!-- Confidence -->
                            <div class="outlook-metric-card">
                                <div class="outlook-metric-label">CONFIDENCE</div>
                                <div class="outlook-confidence">
                                    <div class="outlook-confidence-badge ${confidence.toLowerCase()}">
                                        ${confidence}
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Supporting Evidence Section -->
                        <div class="outlook-evidence-section">
                            <h6 class="outlook-evidence-header">
                                <i class="bi bi-clipboard-data"></i>
                                Supporting Evidence
                                <span class="outlook-evidence-count">${keyEvidence.length}</span>
                            </h6>
                            <div class="outlook-evidence-grid">
                `;
                
                keyEvidence.forEach((evidence) => {
                    let evidenceText = evidence.fact || 'No evidence text available';
                    evidenceText = evidenceText.replace(/\s*\[Article\s+[\d,\s]+\]\s*\.?\s*$/i, '');
                    
                    const hasUrl = evidence.url && evidence.url !== 'N/A';
                    const isMarketData = evidence.article_ref === "MARKET" || evidence.source === "N/A";
                    
                    html += `
                        <div class="outlook-evidence-item ${isMarketData ? 'market-data' : 'news'}">
                            <div class="outlook-evidence-content">
                    `;
                    
                    if (hasUrl && !isMarketData) {
                        html += `
                            <a href="${escapeHtml(evidence.url)}" target="_blank" rel="noopener noreferrer" class="outlook-evidence-link">
                                <div class="outlook-evidence-text">
                                    ${escapeHtml(evidenceText)}
                                </div>
                                <div class="outlook-evidence-meta">
                                    <i class="bi bi-box-arrow-up-right"></i>
                                    <span>Click to read full article</span>
                                </div>
                            </a>
                        `;
                    } else {
                        html += `
                            <div class="outlook-evidence-text">
                                ${escapeHtml(evidenceText)}
                            </div>
                            <div class="outlook-evidence-meta">
                                <i class="bi bi-graph-up"></i>
                                <span>Market data analysis</span>
                            </div>
                        `;
                    }
                    
                    html += `
                            </div>
                        </div>
                    `;
                });
                
                html += `
                            </div>
                        </div>
                    </div>
                `;
            });
            
            html += `
                </div>
            `;
        }

        // Metadata
        if (data.metadata) {
            html += `
                <div class="mt-3 pt-3 border-top">
                    <small class="text-muted">
                        <i class="bi bi-info-circle"></i>
                        Analysis generated using ${escapeHtml(data.metadata.api || 'AI')}
                        ${data.metadata.model ? `(${escapeHtml(data.metadata.model)})` : ''}
                        at ${new Date(data.metadata.timestamp).toLocaleString()}
                    </small>
                </div>
            `;
        }
        
        analysisContainer.innerHTML = html;
    }
    
    switchOutlookTab(term) {
        if (!this.currentOutlookData || !this.currentOutlookId) {
            console.error('No outlook data available for tab switching');
            return;
        }
        
        const randomId = this.currentOutlookId;
        const termOrder = ['short_term', 'medium_term', 'long_term'];
        const outlookTerms = termOrder.filter(t => this.currentOutlookData[t]);
        
        // Hide all content and remove active class
        outlookTerms.forEach(t => {
            const contentEl = document.getElementById(`content_${t}_${randomId}`);
            const tabEl = document.getElementById(`tab_${t}_${randomId}`);
            
            if (contentEl) contentEl.classList.remove('active');
            if (tabEl) tabEl.classList.remove('active');
        });
        
        // Show selected content and activate tab
        const selectedContent = document.getElementById(`content_${term}_${randomId}`);
        const selectedTab = document.getElementById(`tab_${term}_${randomId}`);
        
        if (selectedContent) selectedContent.classList.add('active');
        if (selectedTab) selectedTab.classList.add('active');
    }


    /**
     * Cleanup when component is destroyed
     */
    destroy() {
        if (this.priceGraph) {
            this.priceGraph.destroy();
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.marketAnalysis = new MarketAnalysis();
});