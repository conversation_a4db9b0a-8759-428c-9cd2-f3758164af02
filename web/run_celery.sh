#!/bin/bash

# Celery Email Scheduler Startup Script
# This script starts both Celery worker and beat scheduler

echo "🚀 Starting Celery Email Scheduler..."

# Check if Redis is running
if ! redis-cli ping > /dev/null 2>&1; then
    echo "❌ Redis is not running. Please start Redis first:"
    echo "   redis-server"
    exit 1
fi

echo "✅ Redis is running"

# Load environment variables
if [ -f ../.env ]; then
    echo "📄 Loading environment variables from .env"
    export $(grep -v '^#' ../.env | xargs)
else
    echo "⚠️  No .env file found. Make sure environment variables are set."
fi

# Check required environment variables
required_vars=("MAIL_SERVER" "MAIL_USERNAME" "MAIL_PASSWORD")
missing_vars=()

for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        missing_vars+=("$var")
    fi
done

if [ ${#missing_vars[@]} -ne 0 ]; then
    echo "❌ Missing required environment variables:"
    printf '%s\n' "${missing_vars[@]}"
    echo "Please set these in your .env file"
    exit 1
fi

echo "✅ Environment variables configured"
echo "🔧 Celery Configuration:"
echo "   Broker: ${CELERY_BROKER_URL:-redis://localhost:6379/0}"
echo "   Backend: ${CELERY_RESULT_BACKEND:-redis://localhost:6379/0}"

# Start Celery worker with beat scheduler
echo ""
echo "🎯 Starting Celery worker and beat scheduler..."
echo "   Daily summaries scheduled for 22:00 PM ET"
echo ""

# Run the Python celery worker
celery -A celery_worker.celery worker --loglevel=info --beat