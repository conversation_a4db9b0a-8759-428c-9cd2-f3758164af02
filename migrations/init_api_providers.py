#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to initialize API provider configurations in the database.
This script populates the api_info table with default API providers and models.
"""

import sys
import os
from datetime import datetime

from db.models import ApiInfo, Base

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from db.database import get_db_manager
from db.api_info_service import ApiInfoService
from utils.logging_config import get_db_logger

logger = get_db_logger(__name__)

# Default API provider configurations
DEFAULT_API_PROVIDERS = [
    {
        'provider_name': 'openai',
        'model_name': 'o3',
        'display_name': 'OpenAI o3 (Reasoning)',
        'is_active': True,
        'is_default': False,
        'description': 'OpenAI o3 reasoning model for complex analysis',
        'configuration': {
            'pricing': {
                'input_rate': 0.06,
                'output_rate': 0.24
            },
            'max_tokens': 4096,
            'temperature': 0.1
        }
    },
    {
        'provider_name': 'openai',
        'model_name': 'gpt-4.1-mini',
        'display_name': 'OpenAI GPT-4.1 Mini',
        'is_active': True,
        'is_default': False,
        'description': 'OpenAI GPT-4.1 Mini for cost-effective analysis',
        'configuration': {
            'pricing': {
                'input_rate': 0.00015,
                'output_rate': 0.0006
            },
            'max_tokens': 4096,
            'temperature': 0.1
        }
    },
    {
        'provider_name': 'anthropic',
        'model_name': 'claude-opus-4-20250514',
        'display_name': 'Anthropic Claude Opus 4',
        'is_active': True,
        'is_default': False,
        'description': 'Anthropic Claude Opus 4 for high-quality analysis',
        'configuration': {
            'pricing': {
                'input_rate': 0.015,
                'output_rate': 0.075
            },
            'max_tokens': 4096,
            'temperature': 0.1
        }
    },
    {
        'provider_name': 'anthropic',
        'model_name': 'claude-3-5-haiku-20241022',
        'display_name': 'Anthropic Claude 3.5 Haiku',
        'is_active': True,
        'is_default': False,
        'description': 'Anthropic Claude 3.5 Haiku for fast analysis',
        'configuration': {
            'pricing': {
                'input_rate': 0.0008,
                'output_rate': 0.004
            },
            'max_tokens': 4096,
            'temperature': 0.1
        }
    },
    {
        'provider_name': 'gemini',
        'model_name': 'gemini-2.5-flash',
        'display_name': 'Google Gemini 2.5 Flash',
        'is_active': True,
        'is_default': True,  # Set as default
        'description': 'Google Gemini 2.5 Flash for balanced performance',
        'configuration': {
            'pricing': {
                'input_rate': 0.00075,
                'output_rate': 0.003
            },
            'max_tokens': 4096,
            'temperature': 0.1
        }
    },
    {
        'provider_name': 'gemini',
        'model_name': 'gemini-2.0-flash-001',
        'display_name': 'Google Gemini 2.0 Flash',
        'is_active': True,
        'is_default': False,
        'description': 'Google Gemini 2.0 Flash for analysis',
        'configuration': {
            'pricing': {
                'input_rate': 0.00075,
                'output_rate': 0.003
            },
            'max_tokens': 4096,
            'temperature': 0.1
        }
    }
]

def migrate_api_provider_models():
    try:
        # Initialize database manager
        db_manager = get_db_manager()
        engine = db_manager.connection.engine
        
        # Create the new tables
        print("Creating PromptTemplate and PromptExecution tables...")
        Base.metadata.create_all(bind=engine, tables=[
            ApiInfo.__table__,
        ])
        
        print("✅ Prompt management tables created successfully!")
    except Exception as e:
        logger.error(f"Migration failed: {e}")
        print(f"❌ Migration failed: {e}")
        sys.exit(1)
    finally:
        if 'db_manager' in locals():
            db_manager.close()
    

def init_api_providers():
    """Initialize API provider configurations in the database."""
    try:
        logger.info("Starting API provider initialization...")
        
        # Get database connection
        db_manager = get_db_manager()
        api_service = ApiInfoService(db_manager.connection)
        
        # Check if any API providers already exist
        existing_providers = api_service.get_all_api_info()
        if existing_providers:
            logger.info(f"Found {len(existing_providers)} existing API providers")
            
            # Ask user if they want to continue
            response = input("API providers already exist. Do you want to add new ones anyway? (y/N): ")
            if response.lower() != 'y':
                logger.info("Initialization cancelled by user")
                return
        
        # Add each API provider
        added_count = 0
        skipped_count = 0
        
        for provider_config in DEFAULT_API_PROVIDERS:
            logger.info(f"Adding {provider_config['provider_name']}/{provider_config['model_name']}...")
            
            # Check if this specific provider/model combination already exists
            existing = api_service.get_api_info_by_provider_model(
                provider_config['provider_name'],
                provider_config['model_name']
            )
            
            if existing:
                logger.info(f"Skipping {provider_config['provider_name']}/{provider_config['model_name']} - already exists")
                skipped_count += 1
                continue
            
            # Create the API provider
            result = api_service.create_api_info(provider_config)
            
            if result:
                logger.info(f"Successfully added {provider_config['provider_name']}/{provider_config['model_name']}")
                added_count += 1
            else:
                logger.error(f"Failed to add {provider_config['provider_name']}/{provider_config['model_name']}")
        
        logger.info(f"API provider initialization completed. Added: {added_count}, Skipped: {skipped_count}")
        
        # Show final status
        all_providers = api_service.get_all_api_info()
        logger.info(f"Total API providers in database: {len(all_providers)}")
        
        # Show default provider
        default_provider = api_service.get_default_api_info()
        if default_provider:
            logger.info(f"Default provider: {default_provider['provider_name']}/{default_provider['model_name']}")
        else:
            logger.warning("No default provider set!")
        
    except Exception as e:
        logger.error(f"Error during API provider initialization: {e}")
        raise
    finally:
        if 'db_manager' in locals():
            db_manager.close()


def main():
    """Main function."""
    print("API Provider Initialization Script")
    print("=" * 40)
    
    try:
        migrate_api_provider_models()
        init_api_providers()
        print("\nInitialization completed successfully!")
        
    except KeyboardInterrupt:
        print("\nInitialization cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"\nError during initialization: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
